
import type { Player, ParsedRole, RoleDefinition, RoleDefinitionsContainer } from '../types';

// Centralized place for known player attribute keys and their normalization
// This maps various common HTML header texts (lowercase) to the canonical keys used in the Player type.
const KNOWN_PLAYER_ATTRIBUTES_MAP: Record<string, string> = { // Allow string for unmapped keys
  'name': 'Name',
  'position': 'Position',
  'age': 'Age',
  'height': 'Height',
  'hgt': 'Height',

  // Left Foot variations
  'leftfoot': 'LeftFoot',
  'left foot': 'LeftFoot',
  'lft foot': 'LeftFoot',
  'l. foot': 'LeftFoot',
  'l foot': 'LeftFoot', 
  'left ft.': 'LeftFoot',
  'left_foot': 'LeftFoot',
  'foot (left)': 'LeftFoot',


  // Right Foot variations
  'rightfoot': 'RightFoot',
  'right foot': 'RightFoot',
  'rgt foot': 'RightFoot',
  'r. foot': 'RightFoot',
  'r foot': 'RightFoot', 
  'right ft.': 'RightFoot',
  'right_foot': 'RightFoot',
  'foot (right)': 'RightFoot',

  // Financials - Salary/Wage
  'salary': 'Salary',
  'wage': 'Salary', // Normalize 'Wage' to 'Salary'
  'weekly wage': 'Salary',
  'annual salary': 'Salary',

  // Financials - Transfer Value
  'value': 'Value',
  'transfer value': 'Value', // Normalize 'Transfer Value' to 'Value'
  'market value': 'Value',
  'mkt value': 'Value',
  'est. value': 'Value',
  'estimated value': 'Value',
  'val.': 'Value',
  
  // Nationality
  'nat': 'Nat',
  'nationality': 'Nat',
};

const getNormalizedKey = (header: string): string => {
  const lowerHeader = header.toLowerCase().trim();
  return KNOWN_PLAYER_ATTRIBUTES_MAP[lowerHeader] || header; // Return mapped key or original header if not in map
};


export const parsePlayerHTML = (htmlString: string): Player[] => {
  if (!htmlString.trim()) return [];
  try {
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlString, 'text/html');
    const table = doc.querySelector('table');
    if (!table) {
      console.warn("No <table> element found in HTML string.");
      return [];
    }

    const thElements = Array.from(table.querySelectorAll('th'));
    const rawHeaders = thElements.map(th => th.textContent?.trim() || '');
    
    if (rawHeaders.length === 0) {
        console.warn("No <th> elements found in table.");
        return [];
    }

    const players: Player[] = [];
    table.querySelectorAll('tr').forEach((row, rowIndex) => {
      // Skip header row if it exists and contains <th> elements
      if (rowIndex === 0 && row.querySelector('th')) return; 

      const cells = row.querySelectorAll('td');
      if (cells.length === 0) { // Only skip if there are no cells at all.
        return;
      }
      if (cells.length !== rawHeaders.length && cells.length > 0) {
         // Warn if cell count mismatches header count but still process what's available up to header length
        console.warn(`Row ${rowIndex + 1} has ${cells.length} cells, expected ${rawHeaders.length}. Processing available cells.`);
      }


      const player: Player = {};
      // Iterate up to the minimum of headers length or cells length to prevent errors
      const numberOfCellsToProcess = Math.min(rawHeaders.length, cells.length);
      for (let cellIndex = 0; cellIndex < numberOfCellsToProcess; cellIndex++) {
        const cell = cells[cellIndex];
        const rawHeader = rawHeaders[cellIndex];
        const key = getNormalizedKey(rawHeader); // Use normalized key for Player object
        
        const value = cell.textContent?.trim() || '';

        // Specific handling for known string fields or fields that might be numbers or strings
        if (key === 'Name' || key === 'Position' || key === 'LeftFoot' || key === 'RightFoot' || key === 'Nat') {
          player[key] = value;
        } else if (key === 'Age' || key === 'Height') { // These are expected to be numeric mostly
          const numValue = parseFloat(value);
          player[key] = isNaN(numValue) ? value : numValue; // Store as number if valid, else original string
        } else if (key === 'Salary' || key === 'Value') { // Financial values that might have symbols or text
          const cleanedValue = String(value).replace(/[$,€£KkMm]/g, '').trim(); // Remove common currency symbols and K/M for parsing
          let numValue = parseFloat(cleanedValue);

          if (String(value).toLowerCase().includes('k')) {
            numValue *= 1000;
          } else if (String(value).toLowerCase().includes('m')) {
            numValue *= 1000000;
          }
          
          player[key] = isNaN(numValue) ? value : numValue; // Store as number if valid, else original string (e.g., "On Loan")
        }
         else {
          // For all other attributes, try to parse as number; if NaN, keep as string.
          const numValue = parseFloat(value);
          player[key] = isNaN(numValue) ? value : numValue;
        }
      }

      // Ensure the player object is not empty and has a name or some value
      if (Object.keys(player).length > 0 && (player.Name || Object.values(player).some(v => v !== '' && v !== undefined))) {
         players.push(player);
      }
    });
    return players;
  } catch (error) {
    console.error("Error parsing player HTML:", error);
    throw new Error("Failed to parse player HTML. Ensure it's a valid HTML table snippet.");
  }
};

export const parseRoleJSON = (jsonString: string): ParsedRole[] => {
  if (!jsonString.trim()) return [];
  try {
    const data: unknown = JSON.parse(jsonString);

    if (typeof data === 'object' && data !== null && 'roles' in data && Array.isArray((data as RoleDefinitionsContainer).roles)) {
        const roleContainer = data as RoleDefinitionsContainer;
        return roleContainer.roles.map((role: RoleDefinition) => {
            if (
                typeof role.name === 'string' &&
                typeof role.code === 'string' &&
                typeof role.category === 'string' && 
                Array.isArray(role.key_attributes) && role.key_attributes.every(attr => typeof attr === 'string') &&
                Array.isArray(role.preferable_attributes) && role.preferable_attributes.every(attr => typeof attr === 'string')
            ) {
                return role as ParsedRole;
            } else {
                throw new Error(`Invalid role structure for role: ${role.name || 'Unknown Role'}. Each role must have name, code, category (strings), and key_attributes, preferable_attributes (arrays of strings).`);
            }
        });
    } else {
        throw new Error("Invalid JSON structure. Expected an object with a 'roles' array.");
    }

  } catch (error: any) {
    console.error("Error parsing role JSON:", error);
    if (error instanceof SyntaxError) {
        throw new Error("Invalid JSON format. Please check the syntax.");
    }
    throw new Error(`Failed to parse role JSON: ${error.message}`);
  }
};