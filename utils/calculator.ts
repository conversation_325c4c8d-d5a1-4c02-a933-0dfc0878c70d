
import type { Player, ParsedRole } from '../types';

export const calculateSuitabilityScore = (player: Player, role: ParsedRole): number => {
  let sumKeyAttributesValues = 0;
  let sumPreferableAttributesValues = 0;

  role.key_attributes.forEach(attr => {
    const value = player[attr];
    if (typeof value === 'number') {
      sumKeyAttributesValues += value;
    }
    // If attribute is missing for player, its value is 0 (implicitly handled by sum starting at 0)
  });

  role.preferable_attributes.forEach(attr => {
    const value = player[attr];
    if (typeof value === 'number') {
      sumPreferableAttributesValues += value;
    }
    // If attribute is missing for player, its value is 0 (implicitly handled by sum starting at 0)
  });

  const weightedSumNumerator = (sumKeyAttributesValues * 1.0) + (sumPreferableAttributesValues * 0.8);
  
  const countKeyAttributesInRole = role.key_attributes.length;
  const countPreferableAttributesInRole = role.preferable_attributes.length;
  
  const weightedCountDenominator = (countKeyAttributesInRole * 1.0) + (countPreferableAttributesInRole * 0.8);

  if (weightedCountDenominator === 0) {
    // Avoid division by zero. If a role has no attributes defined, score is 0 or could be handled differently.
    return 0; 
  }

  const score = weightedSumNumerator / weightedCountDenominator;
  
  // Assuming attributes are generally on a 0-20 scale, the score will likely be in this range.
  // No explicit clamping to 0-20 is requested by the formula, but good to be aware.
  // Round to 2 decimal places as requested.
  return parseFloat(score.toFixed(2)); 
};
