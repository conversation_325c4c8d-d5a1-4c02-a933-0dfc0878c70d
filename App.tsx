
import React, { useState, useCallback, useEffect } from 'react';
import { InputSection } from './components/InputSection';
import { ResultsTable } from './components/ResultsTable';
import { Spinner } from './components/Spinner';
import { PlayerAttributesModal } from './components/PlayerAttributesModal'; // Import Modal
import { parsePlayerHTML } from './utils/parser';
import { parseRoleJSON } from './utils/parser';
import { calculateSuitabilityScore } from './utils/calculator';
import type { Player, ParsedRole, CalculatedScore, AggregatedPlayerScore } from './types';

const getNumericPlayerAttribute = (value: string | number | undefined): number | undefined => {
  if (value === undefined || value === null) return undefined;
  const num = Number(value);
  return isNaN(num) ? undefined : num;
};

const getStringPlayerAttribute = (value: string | number | undefined): string | undefined => {
    if (value === undefined || value === null) return undefined;
    const strValue = String(value).trim();
    return strValue === "" ? undefined : strValue;
};

// Attribute key definitions based on data/attributes.json
const GOALKEEPING_ATTRIBUTE_KEYS = ["Aer", "Cmd", "Com", "Ecc", "Han", "Kic", "1v1", "Ref", "Rus", "TRO", "Pun"];
const TECHNICAL_ATTRIBUTE_KEYS = ["Cor", "Cro", "Dri", "Fin", "Fir", "Fre", "Hea", "Lon", "Mar", "Pas", "Pen", "Tck", "Tec", "Thr"];
const MENTAL_ATTRIBUTE_KEYS = ["Agg", "Ant", "Bra", "Cmp", "Cnt", "Dec", "Det", "Fla", "Ldr", "OtB", "Pos", "Tea", "Vis", "Wor"];
const PHYSICAL_ATTRIBUTE_KEYS = ["Acc", "Agi", "Bal", "Jum", "Nat", "Pac", "Sta", "Str"];


const calculateAverageCategoryScore = (player: Player, attributeKeys: string[]): number | undefined => {
  let sum = 0;
  let count = 0;
  attributeKeys.forEach(attr => {
    const value = player[attr];
    if (typeof value === 'number' && !isNaN(value)) {
      sum += value;
      count++;
    }
  });
  if (count === 0) return undefined;
  return parseFloat((sum / count).toFixed(2));
};


const App: React.FC = () => {
  const [playerHtml, setPlayerHtml] = useState<string>('');
  const [roleJson, setRoleJson] = useState<string>('');
  
  const [parsedPlayers, setParsedPlayers] = useState<Player[]>([]);
  const [parsedRoles, setParsedRoles] = useState<ParsedRole[]>([]);
  const [aggregatedScores, setAggregatedScores] = useState<AggregatedPlayerScore[]>([]);
  
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isProcessDisabled, setIsProcessDisabled] = useState(true);

  // State for PlayerAttributesModal
  const [isPlayerModalOpen, setIsPlayerModalOpen] = useState<boolean>(false);
  const [selectedPlayerDataForModal, setSelectedPlayerDataForModal] = useState<AggregatedPlayerScore | null>(null);

  useEffect(() => {
    if (isLoading) {
      setIsProcessDisabled(true);
      return;
    }

    const playerHtmlProvided = playerHtml.trim() !== '';
    let roleJsonIsPresent = roleJson.trim() !== '';
    let rolesAreExplicitlyDeselected = false; 

    if (roleJsonIsPresent) {
        try {
            const parsed = JSON.parse(roleJson);
            if (parsed.roles && Array.isArray(parsed.roles) && parsed.roles.length === 0) {
                rolesAreExplicitlyDeselected = true;
            }
        } catch (e) {
            // Malformed JSON
        }
    }

    if (!playerHtmlProvided && !roleJsonIsPresent) {
        setIsProcessDisabled(true);
    } else if (rolesAreExplicitlyDeselected) {
        setIsProcessDisabled(true);
    } else {
        setIsProcessDisabled(false);
    }
  }, [playerHtml, roleJson, isLoading]);

  const handleProcessData = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    setAggregatedScores([]); 

    let currentPlayers: Player[] = [];
    let currentRoles: ParsedRole[] = [];
    let currentError: string | null = null;

    if (!playerHtml.trim()) {
      currentError = "Player HTML data is empty. Please upload player data.";
    } else {
      try {
        currentPlayers = parsePlayerHTML(playerHtml);
        setParsedPlayers(currentPlayers);
        if (currentPlayers.length === 0) {
          currentError = "No players found in the provided HTML. Ensure the table has <th> for headers and <tr> for player data.";
        }
      } catch (e: any) {
        currentError = `Error parsing Player HTML: ${e.message}. Check console for details.`;
      }
    }

    if (!roleJson.trim()) {
      const roleError = "Role JSON data is empty. Please ensure roles are defined and selected.";
      currentError = currentError ? `${currentError} ${roleError}` : roleError;
    } else {
      try {
        currentRoles = parseRoleJSON(roleJson);
        setParsedRoles(currentRoles);
        if (currentRoles.length === 0) {
          const roleError = "No roles are currently selected. Please select at least one role to proceed.";
          currentError = currentError ? `${currentError} ${roleError}` : roleError;
        }
      } catch (e: any) {
        const roleError = `Error in Role JSON: ${e.message}. Check console for details.`;
        currentError = currentError ? `${currentError} ${roleError}` : roleError;
      }
    }
    
    if (currentError) {
      setError(currentError);
      setIsLoading(false);
      return;
    }

    if (currentPlayers.length === 0 || currentRoles.length === 0) {
        let finalError = "";
        if(currentPlayers.length === 0 && currentRoles.length === 0) {
            finalError = "Both Player HTML and Role JSON data are effectively empty or invalid. Please provide valid data to process.";
        } else if (currentPlayers.length === 0) {
            finalError = "Player HTML data is empty or invalid. Cannot calculate scores.";
        } else { 
            finalError = "Role JSON data is empty, invalid, or no roles are selected. Cannot calculate scores.";
        }
        setError(finalError);
        setIsLoading(false);
        return;
    }

    try {
      const individualScores: CalculatedScore[] = [];
      currentPlayers.forEach(player => {
        currentRoles.forEach(role => {
          const scoreValue = calculateSuitabilityScore(player, role);
          individualScores.push({
            playerName: String(player.Name || 'Unknown Player'),
            roleName: role.name,
            roleCode: role.code,
            suitabilityScore: scoreValue,
          });
        });
      });

      const playerScoresMap = new Map<string, CalculatedScore[]>();
      individualScores.forEach(score => {
        if (!playerScoresMap.has(score.playerName)) {
          playerScoresMap.set(score.playerName, []);
        }
        playerScoresMap.get(score.playerName)!.push(score);
      });

      const newAggregatedScores: AggregatedPlayerScore[] = [];
      currentPlayers.forEach(player => {
        const playerName = String(player.Name || 'Unknown Player');
        const scoresForPlayer = playerScoresMap.get(playerName) || [];
        
        const allCalculatedRolesForPlayer = scoresForPlayer
          .map(s => ({ name: s.roleName, code: s.roleCode, score: s.suitabilityScore }))
          .sort((a, b) => b.score - a.score);

        // Calculate average category scores
        const avgGoalkeepingScore = calculateAverageCategoryScore(player, GOALKEEPING_ATTRIBUTE_KEYS);
        const avgTechnicalScore = calculateAverageCategoryScore(player, TECHNICAL_ATTRIBUTE_KEYS);
        const avgMentalScore = calculateAverageCategoryScore(player, MENTAL_ATTRIBUTE_KEYS);
        const avgPhysicalScore = calculateAverageCategoryScore(player, PHYSICAL_ATTRIBUTE_KEYS);

        if (scoresForPlayer.length > 0) {
          const topRole = allCalculatedRolesForPlayer[0] || null;
          const secondRole = allCalculatedRolesForPlayer[1] || null;
          const thirdRole = allCalculatedRolesForPlayer[2] || null;
          
          newAggregatedScores.push({
            playerName: playerName,
            position: String(player.Position || 'N/A'),
            Age: getNumericPlayerAttribute(player.Age),
            Height: getNumericPlayerAttribute(player.Height),
            LeftFoot: getStringPlayerAttribute(player.LeftFoot),
            RightFoot: getStringPlayerAttribute(player.RightFoot),
            avgGoalkeepingScore,
            avgTechnicalScore,
            avgMentalScore,
            avgPhysicalScore,
            highScore: topRole ? topRole.score : 0,
            topRole: topRole,
            secondRole: secondRole,
            thirdRole: thirdRole,
            allAttributes: player,
            allCalculatedRoles: allCalculatedRolesForPlayer,
          });
        } else {
           newAggregatedScores.push({
            playerName: playerName,
            position: String(player.Position || 'N/A'),
            Age: getNumericPlayerAttribute(player.Age),
            Height: getNumericPlayerAttribute(player.Height),
            LeftFoot: getStringPlayerAttribute(player.LeftFoot),
            RightFoot: getStringPlayerAttribute(player.RightFoot),
            avgGoalkeepingScore,
            avgTechnicalScore,
            avgMentalScore,
            avgPhysicalScore,
            highScore: 0,
            topRole: null,
            secondRole: null,
            thirdRole: null,
            allAttributes: player,
            allCalculatedRoles: [],
          });
        }
      });
      
      setAggregatedScores(newAggregatedScores);

    } catch (e: any) {
      console.error("Processing error:", e);
      setError(`Error processing data: ${e.message}. Check console for more details.`);
    } finally {
      setIsLoading(false);
    }
  }, [playerHtml, roleJson]);

  const handleClearResults = useCallback(() => {
    setAggregatedScores([]);
    setError(null);
  }, []);

  // Modal handlers
  const handleOpenPlayerModal = useCallback((playerData: AggregatedPlayerScore) => {
    setSelectedPlayerDataForModal(playerData);
    setIsPlayerModalOpen(true);
  }, []);

  const handleClosePlayerModal = useCallback(() => {
    setIsPlayerModalOpen(false);
    // Optionally clear data a bit later to avoid visual glitches if modal has fade-out
    // setTimeout(() => setSelectedPlayerDataForModal(null), 300); 
    setSelectedPlayerDataForModal(null);
  }, []);


  return (
    <div className="min-h-screen bg-slate-900 text-slate-100 p-4 md:p-8 flex flex-col items-center selection:bg-purple-500 selection:text-white">
      <header className="mb-8 text-center">
        <h1 className="text-4xl md:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-purple-500">
          Player Role Suitability
        </h1>
        <p className="text-slate-400 mt-2">Analyze player data against role definitions.</p>
      </header>

      <main className="w-full max-w-6xl space-y-8">
        <InputSection
          playerHtml={playerHtml}
          setPlayerHtml={setPlayerHtml}
          roleJson={roleJson}
          setRoleJson={setRoleJson}
          onProcess={handleProcessData}
          isLoading={isLoading}
          isProcessButtonDisabled={isProcessDisabled}
        />

        {error && (
          <div className="bg-red-700/30 border border-red-500 text-red-300 px-4 py-3 rounded-lg relative shadow-lg" role="alert">
            <strong className="font-bold text-red-200">Error: </strong>
            <span className="block sm:inline">{error}</span>
          </div>
        )}

        {isLoading && (
          <div className="flex justify-center items-center py-8">
            <Spinner />
            <p className="ml-3 text-purple-400 text-lg">Processing data...</p>
          </div>
        )}

        {!isLoading && aggregatedScores.length > 0 && (
          <ResultsTable 
            results={aggregatedScores} 
            onClearResults={handleClearResults}
            onOpenPlayerModal={handleOpenPlayerModal} 
          />
        )}
        
        {!isLoading && aggregatedScores.length === 0 && !error && (playerHtml.trim() || roleJson.trim()) && (parsedPlayers.length === 0 || parsedRoles.length === 0) && (
          <div className="bg-slate-800 p-6 rounded-lg shadow-xl text-center">
            <p className="text-slate-400 text-lg">
              Input data provided, but no players or roles were successfully processed to calculate scores.
            </p>
            <p className="text-slate-500 mt-2">
              Please check your inputs or the error message if one was displayed.
            </p>
          </div>
        )}
      </main>

      <footer className="mt-12 text-center text-slate-500 text-sm">
        <p>&copy; {new Date().getFullYear()} Football Analytics Corp. Dark Neon Edition.</p>
      </footer>

      {/* Render PlayerAttributesModal at the App level */}
      {selectedPlayerDataForModal && (
        <PlayerAttributesModal
          isOpen={isPlayerModalOpen}
          onClose={handleClosePlayerModal}
          playerData={selectedPlayerDataForModal}
        />
      )}
    </div>
  );
};

export default App;