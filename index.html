<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Football Player Role Suitability</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    /* Custom scrollbar for webkit browsers */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    ::-webkit-scrollbar-track {
      background: #1e293b; /* slate-800 Darker background for track */
    }
    ::-webkit-scrollbar-thumb {
      background: #8b5cf6; /* Tailwind purple-500 for thumb */
      border-radius: 4px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #6d28d9; /* Tailwind purple-700 for thumb on hover */
    }
    body {
      font-family: 'Inter', sans-serif; /* A clean, modern sans-serif font */
    }
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

    /* Basic styling for code elements */
    code {
      background-color: #334155; /* Tailwind slate-700 */
      color: #cbd5e1; /* Tailwind slate-300 */
      padding: 0.125rem 0.375rem; /* Slightly more horizontal padding */
      border-radius: 0.25rem; /* Tailwind rounded-md */
      font-size: 0.875em; /* text-sm equivalent */
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
      border: 1px solid #475569; /* Tailwind slate-600 */
    }

    /* Custom scrollbar for specific elements if needed, e.g., the roles list */
    .custom-scrollbar::-webkit-scrollbar-track {
      background: #334155; /* slate-700, slightly lighter for contrast within component */
    }
    .custom-scrollbar::-webkit-scrollbar-thumb {
      background: #a78bfa; /* Tailwind violet-400 */
    }
    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
      background: #8b5cf6; /* Tailwind violet-500 */
    }

    /* Animation for success toast */
    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
    .animate-fadeInUp {
      animation: fadeInUp 0.5s ease-out forwards;
    }

  </style>
<script type="importmap">
{
  "imports": {
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "@iconify/react": "https://esm.sh/@iconify/react@^6.0.0"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
<body class="bg-slate-900 text-white">
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html>