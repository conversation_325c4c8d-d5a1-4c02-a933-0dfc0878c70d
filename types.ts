export interface Player {
  [key: string]: string | number; // Player attributes, e.g., { Name: "<PERSON> Do<PERSON>", Hea: 15, Pac: 12 }
  Name?: string;
  Position?: string;
  Age?: number | string; // Parser might yield string initially, will be number in AggregatedPlayerScore
  Height?: number | string; // Parser might yield string initially, will be number in AggregatedPlayerScore
  LeftFoot?: string;
  RightFoot?: string;
}

export interface RoleDefinition {
  name: string;
  code: string;
  category: string; 
  key_attributes: string[];
  preferable_attributes: string[];
}

export interface AppManagedRole extends RoleDefinition {
  id: string; 
  isSelected: boolean; 
}

export interface ParsedRole extends RoleDefinition {} 

export interface CalculatedScore {
  playerName: string;
  roleName: string;
  roleCode: string;
  suitabilityScore: number;
}

export interface AggregatedPlayerScore {
  playerName: string;
  position: string;
  Age?: number;
  Height?: number;
  LeftFoot?: string;
  RightFoot?: string;
  avgGoalkeepingScore?: number;
  avgTechnicalScore?: number;
  avgMentalScore?: number;
  avgPhysicalScore?: number;
  highScore: number;
  topRole: { name: string; code: string; score: number } | null;
  secondRole: { name: string; code: string; score: number } | null;
  thirdRole: { name: string; code: string; score: number } | null;
  allAttributes: Player;
  allCalculatedRoles: { name: string; code: string; score: number }[]; // Added to store all role scores
}

export interface SortConfig {
  key: keyof AggregatedPlayerScore | null;
  direction: 'ascending' | 'descending';
}

export interface AttributeDefinition {
    name: string;
    abbreviation: string;
    description: string;
    category: string;
}

export interface RoleDefinitionsContainer {
    roles: RoleDefinition[]; 
    attributes?: { 
        physical: AttributeDefinition[];
        mental: AttributeDefinition[];
        technical: AttributeDefinition[];
    };
}

export interface AvailableAttributes {
  physical: string[];
  mental: string[];
  technical: string[];
  goalkeeping: string[];
  // Allow other categories if present in attributes.json
  [key: string]: string[];
}