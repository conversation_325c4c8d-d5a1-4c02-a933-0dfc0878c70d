
import React from 'react';

interface IconProps extends React.SVGProps<SVGSVGElement> {}

export const TrashIcon: React.FC<IconProps> = (props) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    fill="none" 
    viewBox="0 0 24 24" 
    strokeWidth={1.5} 
    stroke="currentColor" 
    {...props}
  >
    <path strokeLinecap="round" strokeLinejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12.56 0c1.153 0 2.24.046 3.298.132m-.004-.132C5.023 4.087 3.75 4.017 2.5 4.017m2.272.773A48.321 48.321 0 0112 4.75c2.66 0 5.191.329 7.544.936M2.57 5.791L2.5 6A2.25 2.25 0 004.75 8.25h14.5A2.25 2.25 0 0021.5 6l-.07-.209M4.209 5.791c.294-.08.6-.152.912-.211" />
  </svg>
);
