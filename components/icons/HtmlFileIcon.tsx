import React from 'react';

interface IconProps extends React.SVGProps<SVGSVGElement> {}

export const HtmlFileIcon: React.FC<IconProps> = (props) => (
  <svg 
    viewBox="0 0 80 96" 
    fill="none" 
    xmlns="http://www.w3.org/2000/svg" 
    {...props}
  >
    <defs>
      <linearGradient id="fileIconPurplePinkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style={{ stopColor: '#A770EF' }} /> {/* Purple */}
        <stop offset="100%" style={{ stopColor: '#F79489' }} /> {/* Pinkish */}
      </linearGradient>
    </defs>
    {/* File body with a dark fill matching the uploader's inner background */}
    <path 
      d="M52 0H8C3.58172 0 0 3.58172 0 8V88C0 92.4183 3.58172 96 8 96H72C76.4183 96 80 92.4183 80 88V24L52 0Z" 
      fill="#1E293B" // slate-800 (darker than slate-700/30, more solid)
    />
    {/* File outline with gradient */}
    <path 
      d="M52 0H8C3.58172 0 0 3.58172 0 8V88C0 92.4183 3.58172 96 8 96H72C76.4183 96 80 92.4183 80 88V24L52 0Z" 
      stroke="url(#fileIconPurplePinkGradient)" 
      strokeWidth="3"
    />
    {/* Folded corner */}
    <path d="M52 0V24H80" stroke="url(#fileIconPurplePinkGradient)" strokeWidth="3"/>

    {/* HTML label background */}
    <rect x="18" y="38" width="44" height="20" rx="4" fill="#334155" /* slate-700 */ />
    <rect x="18" y="38" width="44" height="20" rx="4" stroke="url(#fileIconPurplePinkGradient)" strokeWidth="1.5"/>

    {/* HTML text with gradient fill */}
    <text 
      x="40" // Centered horizontally
      y="48.5" // Adjusted for vertical centering in the 20px high rect
      fontFamily="Inter, Arial, sans-serif" 
      fontSize="10" 
      fontWeight="bold"
      fill="url(#fileIconPurplePinkGradient)"
      textAnchor="middle" 
      dominantBaseline="middle"
    >
      HTML
    </text>
  </svg>
);
