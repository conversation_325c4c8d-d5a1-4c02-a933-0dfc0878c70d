import React from 'react';

interface IconProps extends React.SVGProps<SVGSVGElement> {}

export const CheckboxCheckedIcon: React.FC<IconProps> = (props) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    viewBox="0 0 20 20" 
    fill="none"
    {...props}
    // RoleInput provides text-green-400 for the checkmark.
    className={`w-5 h-5 ${props.className || 'text-green-400'}`} 
  >
    {/* Border of the checkbox, fixed violet color */}
    <rect 
      x="3.5" y="3.5" width="13" height="13" rx="2" 
      stroke="#A78BFA" /* Tailwind violet-400 */ 
      strokeWidth="1.5" 
      fill="none" 
    />
    {/* Checkmark, uses currentColor (set by className) for its stroke */}
    <path 
      d="M14.5 7L8.25 13.25L5.5 10.5" // A common checkmark shape
      stroke="currentColor" 
      strokeWidth="2" 
      strokeLinecap="round" 
      strokeLinejoin="round"
      fill="none"
    />
  </svg>
);