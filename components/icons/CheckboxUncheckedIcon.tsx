import React from 'react';

interface IconProps extends React.SVGProps<SVGSVGElement> {}

export const CheckboxUncheckedIcon: React.FC<IconProps> = (props) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    viewBox="0 0 20 20" 
    fill="none" 
    stroke="currentColor" // Uses text color for the stroke
    {...props}
    // Default class can set a base color, parent can override. RoleInput provides text-purple-400.
    className={`w-5 h-5 ${props.className || 'text-purple-400'}`} 
  >
    <rect x="3.5" y="3.5" width="13" height="13" rx="2" strokeWidth="1.5" />
  </svg>
);