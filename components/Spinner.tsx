import React from 'react';

// Explicitly define props including className for clarity and robustness
interface SpinnerProps {
  className?: string;
}

export const Spinner: React.FC<SpinnerProps> = ({ className }) => {
  // Default classes for the spinner's appearance and animation
  const defaultSpinnerClasses = "animate-spin h-8 w-8 text-purple-500";
  
  // Combine default classes with any provided className prop.
  // TailwindCSS merges classes, so later classes can override earlier ones.
  const combinedClassName = `${defaultSpinnerClasses} ${className || ''}`.trim();

  return (
    <svg 
      className={combinedClassName} // Use the combined className
      xmlns="http://www.w3.org/2000/svg" 
      fill="none" 
      viewBox="0 0 24 24"
      aria-label="Loading..."
      role="img" 
    >
      <circle 
        className="opacity-25" 
        cx="12" 
        cy="12" 
        r="10" 
        stroke="currentColor" 
        strokeWidth="4"
      ></circle>
      <path 
        className="opacity-75" 
        fill="currentColor" 
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        style={{ filter: 'drop-shadow(0 0 3px #32ffe9)' }} 
      ></path>
    </svg>
  );
};