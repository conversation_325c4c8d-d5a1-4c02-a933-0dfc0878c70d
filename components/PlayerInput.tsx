import React, { useCallback, useRef, useState } from 'react';
import { HtmlFileIcon } from './icons/HtmlFileIcon'; // New Icon

interface PlayerInputProps {
  setPlayerHtml: (html: string) => void;
  disabled?: boolean;
}

interface FeedbackState {
  type: 'success' | 'error' | null;
  message: string | null;
}

export const PlayerInput: React.FC<PlayerInputProps> = ({ setPlayerHtml, disabled }) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDraggingOver, setIsDraggingOver] = useState(false);
  const [feedback, setFeedback] = useState<FeedbackState>({ type: null, message: null });

  const processSingleFile = useCallback((file: File | undefined | null) => {
    if (disabled) return;
    if (file) {
      if (file.type === "text/html" || file.name.toLowerCase().endsWith('.html') || file.name.toLowerCase().endsWith('.htm')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const fileContent = e.target?.result as string;
          setPlayerHtml(fileContent);
          setFeedback({ type: 'success', message: `Successfully uploaded: ${file.name}` });
        };
        reader.onerror = () => {
          setFeedback({ type: 'error', message: `Error reading file: ${file.name}`});
          setPlayerHtml(''); // Clear content on error
        }
        reader.readAsText(file);
      } else {
        setFeedback({ type: 'error', message: `Invalid file: ${file.name}. Please upload an HTML file.` });
        setPlayerHtml(''); // Clear content if invalid file type
      }
    } else {
      // Optionally clear feedback if no file is selected (e.g., user cancels dialog)
      // setFeedback({ type: null, message: null });
    }
  }, [setPlayerHtml, disabled]);

  const handleFileChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    processSingleFile(event.target.files?.[0]);
    if(event.target) event.target.value = ''; // Reset file input to allow re-uploading the same file
  }, [processSingleFile]);

  const triggerFileInput = () => {
    if (disabled) return;
    // Clear previous feedback when initiating a new file selection
    setFeedback({ type: null, message: null });
    fileInputRef.current?.click();
  };

  const handleDragEnter = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    if (disabled) return;
    event.preventDefault();
    event.stopPropagation();
    // Clear feedback when a new drag operation begins
    setFeedback({ type: null, message: null });
    if (event.dataTransfer.items && event.dataTransfer.items.length > 0) {
      setIsDraggingOver(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    if (disabled) return;
    event.preventDefault();
    event.stopPropagation();
    const dropZone = event.currentTarget;
    if (!dropZone.contains(event.relatedTarget as Node)) {
        setIsDraggingOver(false);
    }
  }, [disabled]);

  const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    if (disabled) return;
    event.preventDefault();
    event.stopPropagation();
    if (!isDraggingOver && event.dataTransfer.items && event.dataTransfer.items.length > 0) {
        setIsDraggingOver(true);
    }
  }, [disabled, isDraggingOver]);

  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    if (disabled) return;
    event.preventDefault();
    event.stopPropagation();
    setIsDraggingOver(false);
    // Feedback is set by processSingleFile
    processSingleFile(event.dataTransfer.files?.[0]);
    if (event.dataTransfer.items) {
      event.dataTransfer.items.clear();
    } else {
      event.dataTransfer.clearData();
    }
  }, [disabled, processSingleFile]);

  return (
    <div
      className={`relative rounded-2xl p-1 bg-gradient-to-br from-purple-600 to-cyan-500 shadow-xl
                  transition-all duration-300 ease-in-out h-full
                  ${isDraggingOver ? 'ring-4 ring-pink-400 ring-opacity-70 scale-[1.02]' : ''}
                  ${disabled ? 'opacity-60 pointer-events-none' : ''}`}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
      aria-disabled={disabled}
      aria-label="Player data HTML file drop zone"
    >
      <div className={`flex flex-col items-center justify-center text-center 
                     bg-slate-900 text-slate-100 p-6 sm:p-8 md:p-10 
                     rounded-[0.9rem] h-full min-h-[300px] sm:min-h-[350px] md:min-h-[380px] space-y-4 md:space-y-5`}
      >
        <HtmlFileIcon className="w-20 h-24 sm:w-24 sm:h-28 text-slate-700/50" aria-hidden="true" />
        
        <p className="text-lg sm:text-xl font-semibold text-slate-100">
          Drag and drop HTML file here
        </p>
        
        <p className="text-sm text-pink-400">
          or
        </p>

        <div className={`rounded-lg p-[2px] bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 
                        ${disabled ? '' : 'hover:opacity-90'} transition-opacity`}>
          <button
            onClick={triggerFileInput}
            disabled={disabled}
            className={`w-full bg-slate-800 hover:bg-slate-700/80 px-6 py-2.5 sm:px-8 sm:py-3 rounded-[5px] 
                       text-sm sm:text-base font-semibold transition-colors duration-200
                       disabled:bg-slate-700 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-opacity-60`}
            aria-label="Upload HTML file for player data"
          >
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-300 via-purple-400 to-pink-300">
              Upload file
            </span>
          </button>
        </div>
        
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept=".html,.htm,text/html"
          className="hidden"
          disabled={disabled}
          aria-label="Player data HTML file input"
        />

        {feedback.message && (
          <p 
            className={`mt-3 text-xs sm:text-sm px-2 py-1 rounded-md
                        ${feedback.type === 'success' ? 'text-green-400 bg-green-700/20 border border-green-600/30' : ''}
                        ${feedback.type === 'error' ? 'text-red-400 bg-red-700/20 border border-red-600/30' : ''}`}
            role={feedback.type === 'error' ? 'alert' : 'status'}
          >
            {feedback.message}
          </p>
        )}
      </div>
    </div>
  );
};