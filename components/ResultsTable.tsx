
import React, { useState, useMemo, useCallback, useRef, useEffect } from 'react';
import type { AggregatedPlayerScore, SortConfig, Player } from '../types';
import { SortIcon } from './icons/SortIcon';
import { SortAscIcon } from './icons/SortAscIcon';
import { SortDescIcon } from './icons/SortDescIcon';
import { TrashIcon } from './icons/TrashIcon';
import { EyeIcon } from './icons/EyeIcon';
import { SearchIcon } from './icons/SearchIcon';
import { FilterIcon } from './icons/FilterIcon';
import { Icon } from '@iconify/react';

interface ResultsTableProps {
  results: AggregatedPlayerScore[];
  onClearResults: () => void;
  onOpenPlayerModal: (playerData: AggregatedPlayerScore) => void;
}

const BASE_HEADERS_CONFIG: {
  key: keyof AggregatedPlayerScore | 'actionsColumn';
  label: string;
  sortable: boolean;
  isToggleable: boolean;
  defaultVisible: boolean;
  tooltip?: string;
  align?: 'left' | 'center' | 'right';
}[] = [
    { key: 'playerName', label: 'Player Name', sortable: true, isToggleable: false, defaultVisible: true, align: 'left' },
    { key: 'position', label: 'Position', sortable: true, isToggleable: false, defaultVisible: true, align: 'left' },
    { key: 'Age', label: 'Age', sortable: true, isToggleable: true, defaultVisible: true, align: 'center' },
    { key: 'Height', label: 'Height (cm)', sortable: true, isToggleable: true, defaultVisible: true, align: 'center' },
    { key: 'LeftFoot', label: 'Left Foot', sortable: false, isToggleable: true, defaultVisible: true, align: 'center' },
    { key: 'RightFoot', label: 'Right Foot', sortable: false, isToggleable: true, defaultVisible: true, align: 'center' },
    { key: 'avgGoalkeepingScore', label: 'GK Avg', sortable: true, isToggleable: true, defaultVisible: false, tooltip: 'Average Goalkeeping Attributes Score', align: 'center' },
    { key: 'avgTechnicalScore', label: 'Tech Avg', sortable: true, isToggleable: true, defaultVisible: false, tooltip: 'Average Technical Attributes Score', align: 'center' },
    { key: 'avgMentalScore', label: 'Men Avg', sortable: true, isToggleable: true, defaultVisible: false, tooltip: 'Average Mental Attributes Score', align: 'center' },
    { key: 'avgPhysicalScore', label: 'Phy Avg', sortable: true, isToggleable: true, defaultVisible: false, tooltip: 'Average Physical Attributes Score', align: 'center' },
    { key: 'highScore', label: 'High Score', sortable: true, isToggleable: true, defaultVisible: true, tooltip: 'Highest Role Suitability Score', align: 'center' },
    { key: 'topRole', label: 'Top Role', sortable: false, isToggleable: true, defaultVisible: true, align: 'center' },
    { key: 'secondRole', label: '2nd Role', sortable: false, isToggleable: true, defaultVisible: false, align: 'center' },
    { key: 'thirdRole', label: '3rd Role', sortable: false, isToggleable: true, defaultVisible: false, align: 'center' },
    { key: 'actionsColumn', label: 'Actions', sortable: false, isToggleable: true, defaultVisible: true, align: 'center' },
];

const initialVisibleColumns = () => {
  const initial: Record<string, boolean> = {};
  BASE_HEADERS_CONFIG.forEach(header => {
    if (header.isToggleable) {
      initial[header.key as string] = header.defaultVisible;
    }
  });
  return initial;
};

const getFootIconColor = (footStrength: string | undefined): string => {
  if (footStrength === undefined || footStrength === null || footStrength.trim() === "") {
    return 'text-slate-500';
  }
  const strength = String(footStrength).trim().toLowerCase();
  switch (strength) {
    case 'very strong': return 'text-cyan-400';
    case 'strong': return 'text-green-400';
    case 'reasonable': return 'text-slate-300';
    case 'fairly strong': return 'text-orange-400'; // Changed from "fairly good" to "fairly strong" to match potential data
    case 'weak': return 'text-red-400';
    default:
      console.warn(`Unknown foot strength: "${footStrength}", defaulting icon color.`);
      return 'text-yellow-500';
  }
};

const getAverageScoreColor = (value: number | undefined): string => {
  if (value === undefined || isNaN(Number(value))) return 'text-slate-400';
  const numValue = Number(value);
  if (numValue >= 15) return 'text-green-400';
  if (numValue >= 10) return 'text-sky-400';
  if (numValue >= 1) return 'text-yellow-500';
  return 'text-slate-400';
};


export const ResultsTable: React.FC<ResultsTableProps> = ({ results, onClearResults, onOpenPlayerModal }) => {
  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: 'highScore', direction: 'descending' });

  const [searchTerm, setSearchTerm] = useState<string>('');
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState<boolean>(false);
  const [visibleColumns, setVisibleColumns] = useState<Record<string, boolean>>(initialVisibleColumns());
  const [tempHighScoreRange, setTempHighScoreRange] = useState<{ min: string; max: string }>({ min: '', max: '' });
  const [appliedHighScoreRange, setAppliedHighScoreRange] = useState<{ min: string; max: string }>({ min: '', max: '' });

  const filterButtonRef = useRef<HTMLButtonElement>(null);
  const scrollableContainerRef = useRef<HTMLDivElement>(null);

  // State for click and drag scrolling
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeftStart, setScrollLeftStart] = useState(0);


  // Pagination state
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(5); // Default items per page set to 5
  const itemsPerPageOptions = [5, 10, 25, 50, 100];


  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setCurrentPage(1); // Reset to first page on new search
  };

  const toggleFilterPanel = () => {
    setIsFilterPanelOpen(prev => !prev);
    if (!isFilterPanelOpen) {
        setTempHighScoreRange(appliedHighScoreRange);
    }
  };

  const handleColumnToggle = (key: string) => {
    setVisibleColumns(prev => ({ ...prev, [key]: !prev[key] }));
  };

  const handleTempHighScoreChange = (event: React.ChangeEvent<HTMLInputElement>, type: 'min' | 'max') => {
    setTempHighScoreRange(prev => ({ ...prev, [type]: event.target.value }));
  };

  const applyFiltersAndClose = () => {
    setAppliedHighScoreRange(tempHighScoreRange);
    setCurrentPage(1); // Reset to first page when filters are applied
    setIsFilterPanelOpen(false);
  };

  const resetFilters = () => {
    setVisibleColumns(initialVisibleColumns());
    setTempHighScoreRange({ min: '', max: '' });
    setAppliedHighScoreRange({ min: '', max: '' });
    setCurrentPage(1); // Reset to first page when filters are reset
  };

  const filteredAndSortedResults = useMemo(() => {
    let filteredItems = [...results];

    if (searchTerm.trim() !== '') {
      const lowerSearchTerm = searchTerm.toLowerCase();
      filteredItems = filteredItems.filter(item =>
        item.playerName.toLowerCase().includes(lowerSearchTerm) ||
        (item.Age !== undefined && String(item.Age).includes(searchTerm)) ||
        (item.Height !== undefined && String(item.Height).includes(searchTerm)) ||
        (item.LeftFoot && item.LeftFoot.toLowerCase().includes(lowerSearchTerm)) ||
        (item.RightFoot && item.RightFoot.toLowerCase().includes(lowerSearchTerm)) ||
        (item.position && item.position.toLowerCase().includes(lowerSearchTerm))
      );
    }

    const minScore = parseFloat(appliedHighScoreRange.min);
    const maxScore = parseFloat(appliedHighScoreRange.max);

    if (!isNaN(minScore) || !isNaN(maxScore)) {
      filteredItems = filteredItems.filter(item => {
        const score = item.highScore; 
        const passesMin = isNaN(minScore) || score >= minScore;
        const passesMax = isNaN(maxScore) || score <= maxScore;
        return passesMin && passesMax;
      });
    }

    if (sortConfig.key !== null) {
      filteredItems.sort((a, b) => {
        const valA = a[sortConfig.key!];
        const valB = b[sortConfig.key!];

        if (valA === undefined || valA === null) return sortConfig.direction === 'ascending' ? 1 : -1;
        if (valB === undefined || valB === null) return sortConfig.direction === 'ascending' ? -1 : 1;

        if (typeof valA === 'number' && typeof valB === 'number') {
          return sortConfig.direction === 'ascending' ? valA - valB : valB - valA;
        }

        if (sortConfig.key === 'Age' || sortConfig.key === 'Height') {
            const numA = parseFloat(String(valA));
            const numB = parseFloat(String(valB));
            if (!isNaN(numA) && !isNaN(numB)) {
                 return sortConfig.direction === 'ascending' ? numA - numB : numB - numA;
            }
        }

        const strA = String(valA).toLowerCase();
        const strB = String(valB).toLowerCase();

        if (strA < strB) {
          return sortConfig.direction === 'ascending' ? -1 : 1;
        }
        if (strA > strB) {
          return sortConfig.direction === 'ascending' ? 1 : -1;
        }
        return 0;
      });
    }
    return filteredItems;
  }, [results, searchTerm, appliedHighScoreRange, sortConfig]);

  // Pagination Calculations
  const totalItems = filteredAndSortedResults.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  useEffect(() => {
    const newTotalPages = Math.ceil(filteredAndSortedResults.length / itemsPerPage);
    // Ensure currentPage is valid, between 1 and totalPages (or 1 if no pages)
    const newCurrentPage = Math.min(Math.max(1, currentPage), newTotalPages > 0 ? newTotalPages : 1);
    if (currentPage !== newCurrentPage) {
        setCurrentPage(newCurrentPage);
    }
  }, [filteredAndSortedResults.length, itemsPerPage, currentPage]);

  const currentTableData = useMemo(() => {
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    return filteredAndSortedResults.slice(indexOfFirstItem, indexOfLastItem);
  }, [filteredAndSortedResults, currentPage, itemsPerPage]);

  const handleItemsPerPageChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setItemsPerPage(Number(event.target.value));
    setCurrentPage(1); // Reset to first page
  };

  const handlePreviousPage = () => {
    setCurrentPage(prev => Math.max(prev - 1, 1));
  };

  const handleNextPage = () => {
    setCurrentPage(prev => Math.min(prev + 1, totalPages));
  };


  const displayedHeaders = useMemo(() => {
    return BASE_HEADERS_CONFIG.filter(header => {
      return !header.isToggleable || visibleColumns[header.key as string];
    });
  }, [visibleColumns]);


  const requestSort = useCallback((key: keyof AggregatedPlayerScore) => {
    let direction: 'ascending' | 'descending' = 'ascending';
    if (sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    } else if (sortConfig.key === key && sortConfig.direction === 'descending') {
       direction = 'ascending'; // Allow toggling back to ascending
    }
    setSortConfig({ key, direction });
    setCurrentPage(1); // Reset to first page on sort change
  }, [sortConfig]);


  // Click and Drag Scroll Handlers
  const handleDragStart = useCallback((ePageX: number) => {
    if (!scrollableContainerRef.current) return;
    setIsDragging(true);
    setStartX(ePageX - scrollableContainerRef.current.offsetLeft);
    setScrollLeftStart(scrollableContainerRef.current.scrollLeft);
    scrollableContainerRef.current.style.cursor = 'grabbing';
    scrollableContainerRef.current.style.userSelect = 'none';
  }, []);

  const handleDragMove = useCallback((ePageX: number) => {
    if (!isDragging || !scrollableContainerRef.current) return;
    const x = ePageX - scrollableContainerRef.current.offsetLeft;
    const walk = (x - startX); // Adjust sensitivity if needed: * 1.5;
    scrollableContainerRef.current.scrollLeft = scrollLeftStart - walk;
  }, [isDragging, startX, scrollLeftStart]);

  const handleDragEnd = useCallback(() => {
    if (!scrollableContainerRef.current) return;
    setIsDragging(false);
    scrollableContainerRef.current.style.cursor = 'grab';
    scrollableContainerRef.current.style.userSelect = '';
  }, []);

  // Mouse event handlers
  const onMouseDown = useCallback((e: React.MouseEvent<HTMLDivElement>) => handleDragStart(e.pageX), [handleDragStart]);
  
  // Touch event handlers
  const onTouchStart = useCallback((e: React.TouchEvent<HTMLDivElement>) => handleDragStart(e.touches[0].pageX), [handleDragStart]);

  useEffect(() => {
    const currentRef = scrollableContainerRef.current;
    if (!currentRef) return;

    const onMouseMove = (e: MouseEvent) => handleDragMove(e.pageX);
    const onMouseUp = () => handleDragEnd();
    
    const onTouchMove = (e: TouchEvent) => {
      if (isDragging) {
        e.preventDefault(); // Prevent page scroll while dragging horizontally
        handleDragMove(e.touches[0].pageX);
      }
    };
    const onTouchEnd = () => handleDragEnd();

    if (isDragging) {
      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp);
      document.addEventListener('touchmove', onTouchMove, { passive: false });
      document.addEventListener('touchend', onTouchEnd);
    }

    return () => {
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
      document.removeEventListener('touchmove', onTouchMove);
      document.removeEventListener('touchend', onTouchEnd);
    };
  }, [isDragging, handleDragMove, handleDragEnd]);


  const getSortIcon = (key: keyof AggregatedPlayerScore | 'actionsColumn') => {
    if (!BASE_HEADERS_CONFIG.find(h => h.key === key)?.sortable) return null;
    if (sortConfig.key !== key) {
      return <SortIcon className="ml-1 w-3 h-3 sm:w-4 sm:h-4 text-slate-500 group-hover:text-cyan-400" />;
    }
    if (sortConfig.direction === 'ascending') {
      return <SortAscIcon className="ml-1 w-3 h-3 sm:w-4 sm:h-4 text-cyan-400" />;
    }
    return <SortDescIcon className="ml-1 w-3 h-3 sm:w-4 sm:h-4 text-cyan-400" />;
  };

  const renderRoleCell = (role: { name: string; code: string; score: number } | null, colorClass: string) => {
    if (!role) return <span className="text-slate-500">—</span>;
    return (
      <div className="flex flex-col">
        <span className={`${colorClass} font-medium`}>{role.name}</span>
        <span className="text-xs text-slate-400">({role.score.toFixed(2)})</span>
      </div>
    );
  };

  const renderAverageScoreCell = (score: number | undefined) => {
    if (score === undefined) return <span className="text-slate-500">—</span>;
    return <span className={`${getAverageScoreColor(score)} font-semibold`}>{score.toFixed(2)}</span>;
  };

  if (results.length === 0 && searchTerm.trim() === '' && appliedHighScoreRange.min === '' && appliedHighScoreRange.max === '') {
    return null;
  }

  return (
    <>
      <section className="bg-slate-800 p-4 md:p-6 rounded-xl shadow-2xl border border-slate-700">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 md:mb-6 gap-4">
          <h2 className="text-xl md:text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 to-purple-400">
            Calculated Scores
          </h2>
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 w-full sm:w-auto">
            <button
                ref={filterButtonRef}
                onClick={toggleFilterPanel}
                className="flex items-center justify-center gap-2 py-2 px-3 sm:px-4 bg-sky-600 hover:bg-sky-700
                           text-white text-sm font-medium rounded-md transition-colors duration-200
                           focus:outline-none focus:ring-2 focus:ring-sky-400 focus:ring-opacity-50
                           shadow-md hover:shadow-lg"
                aria-expanded={isFilterPanelOpen}
                aria-controls="filter-panel-results"
            >
                <FilterIcon className="w-4 h-4 sm:w-5 sm:h-5"/>
                Filter Options
            </button>
            <button
                onClick={onClearResults}
                className="flex items-center justify-center gap-2 py-2 px-3 sm:px-4 bg-red-600 hover:bg-red-700
                           text-white text-sm font-medium rounded-md transition-colors duration-200
                           focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-opacity-50
                           shadow-md hover:shadow-lg"
            >
                <TrashIcon className="w-4 h-4 sm:w-5 sm:h-5"/>
                Clear Results
            </button>
          </div>
        </div>

        <div className="mb-2 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <SearchIcon className="w-5 h-5 text-slate-400" />
            </div>
            <input
                type="text"
                placeholder="Search players by name, position, age, height, foot strength..."
                value={searchTerm}
                onChange={handleSearchChange}
                className="w-full p-2.5 pl-10 bg-slate-700 text-slate-200 border-2 border-slate-600 rounded-lg
                           focus:border-purple-500 focus:ring-1 focus:ring-purple-500 transition-colors"
                aria-label="Search players by name or attributes"
            />
        </div>
        <p className="text-xs text-slate-400 mb-4 text-center sm:text-left">
          Tip: Use 'Filter Options' to customize visible columns. Click and drag table to scroll.
        </p>

        {isFilterPanelOpen && (
            <div
                id="filter-panel-results"
                className="relative bg-slate-700 p-4 md:p-6 rounded-lg shadow-xl mb-6 border border-slate-600"
                role="region"
                aria-labelledby="filter-panel-title"
            >
                <h3 id="filter-panel-title" className="text-lg font-semibold text-purple-300 mb-4">Filter Options</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <h4 className="text-md font-medium text-slate-200 mb-2">Visible Columns</h4>
                        <div className="space-y-2 max-h-60 overflow-y-auto custom-scrollbar pr-2">
                            {BASE_HEADERS_CONFIG.filter(h => h.isToggleable).map(header => (
                                <label key={header.key} className="flex items-center space-x-2 cursor-pointer text-slate-300 hover:text-slate-100">
                                    <input
                                        type="checkbox"
                                        checked={!!visibleColumns[header.key as string]}
                                        onChange={() => handleColumnToggle(header.key as string)}
                                        className="form-checkbox h-4 w-4 text-purple-500 bg-slate-600 border-slate-500 rounded
                                                   focus:ring-purple-400 focus:ring-offset-slate-700"
                                        aria-labelledby={`col-toggle-${header.key}`}
                                    />
                                    <span id={`col-toggle-${header.key}`} title={header.tooltip}>{header.label}</span>
                                </label>
                            ))}
                        </div>
                    </div>
                    <div>
                        <h4 className="text-md font-medium text-slate-200 mb-2">High Score Range (Role Suitability)</h4>
                        <div className="flex items-center gap-2 mb-2">
                            <input
                                type="number"
                                placeholder="Min"
                                value={tempHighScoreRange.min}
                                onChange={(e) => handleTempHighScoreChange(e, 'min')}
                                className="w-full p-2 bg-slate-600 text-slate-200 border border-slate-500 rounded-md focus:border-purple-400 focus:ring-1 focus:ring-purple-400 placeholder-slate-400"
                                aria-label="Minimum high score"
                            />
                            <span className="text-slate-400">-</span>
                            <input
                                type="number"
                                placeholder="Max"
                                value={tempHighScoreRange.max}
                                onChange={(e) => handleTempHighScoreChange(e, 'max')}
                                className="w-full p-2 bg-slate-600 text-slate-200 border border-slate-500 rounded-md focus:border-purple-400 focus:ring-1 focus:ring-purple-400 placeholder-slate-400"
                                aria-label="Maximum high score"
                            />
                        </div>
                    </div>
                </div>

                <div className="flex flex-col sm:flex-row justify-end gap-3 pt-4 border-t border-slate-600">
                    <button
                        onClick={resetFilters}
                        className="px-4 py-2 text-slate-300 bg-slate-600 hover:bg-slate-500 rounded-md transition-colors
                                   text-sm font-medium flex items-center justify-center gap-2
                                   focus:outline-none focus:ring-2 focus:ring-slate-400"
                    >
                        <TrashIcon className="w-4 h-4"/> Reset Filters
                    </button>
                    <button
                        onClick={applyFiltersAndClose}
                        className="px-4 py-2 text-white bg-gradient-to-r from-purple-600 to-cyan-500 hover:from-purple-700 hover:to-cyan-600
                                   rounded-md transition-all shadow-md hover:shadow-lg text-sm font-medium
                                   focus:outline-none focus:ring-2 focus:ring-purple-400"
                    >
                        Apply Filters & Close
                    </button>
                </div>
            </div>
        )}

        <div
          ref={scrollableContainerRef}
          className="overflow-x-auto rounded-lg shadow-md border border-slate-700 cursor-grab"
          onMouseDown={onMouseDown}
          onTouchStart={onTouchStart}
          role="region"
          aria-roledescription="scrollable table"
          aria-label="Calculated player scores, scrollable horizontally by dragging"
        >
          <table className="w-full min-w-[900px] md:min-w-[1200px] text-left text-xs sm:text-sm">
            <thead className="bg-slate-700/50">
              <tr>
                {displayedHeaders.map((header) => (
                  <th
                    key={header.key as string}
                    onClick={() => header.sortable && requestSort(header.key as keyof AggregatedPlayerScore)}
                    className={`px-3 py-2 md:px-4 md:py-3 font-semibold uppercase tracking-wider text-cyan-400
                                ${header.align === 'left' ? 'text-left' : 'text-center'}
                                ${header.sortable ? 'cursor-pointer group hover:bg-slate-700' : ''}
                                transition-colors duration-150 select-none`} // Added select-none to prevent text selection issues on header
                    aria-sort={header.sortable && sortConfig.key === header.key ? (sortConfig.direction === 'ascending' ? 'ascending' : 'descending') : 'none'}
                    title={header.tooltip}
                  >
                    <div className={`flex items-center ${header.align === 'center' ? 'justify-center' : ''}`}>
                      {header.label}
                      {getSortIcon(header.key as keyof AggregatedPlayerScore | 'actionsColumn')}
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-slate-800 divide-y divide-slate-700">
              {currentTableData.map((item, index) => (
                <tr key={`${item.playerName}-${index}`} className="hover:bg-slate-700/70 transition-colors duration-150">
                  {displayedHeaders.map((header) => (
                     <td key={`${header.key as string}-${item.playerName}-${index}`}
                         className={`px-3 py-2.5 md:px-4 md:py-3 align-middle
                                     ${header.align === 'left' ? 'text-left' : 'text-center'}`}>
                        {header.key === 'playerName' && <span className="text-slate-200 font-medium block">{item.playerName}</span>}
                        {header.key === 'position' && <span className="text-purple-400 block">{item.position}</span>}
                        {header.key === 'Age' && (
                            <span className="text-slate-300">
                                {item.Age !== undefined ? item.Age : <span className="text-slate-500">—</span>}
                            </span>
                        )}
                        {header.key === 'Height' && (
                            <span className="text-slate-300">
                                {item.Height !== undefined ? `${item.Height} cm` : <span className="text-slate-500">—</span>}
                            </span>
                        )}
                        {header.key === 'LeftFoot' && (
                            item.LeftFoot && item.LeftFoot.trim() !== "" ?
                            <span title={`Left Foot: ${item.LeftFoot}`}>
                                <Icon icon="mdi:foot-print" className={`w-5 h-5 inline-block ${getFootIconColor(item.LeftFoot)}`} aria-label={`Left foot strength: ${item.LeftFoot}`} />
                            </span>
                            : <span className="text-slate-500">—</span>
                        )}
                        {header.key === 'RightFoot' && (
                            item.RightFoot && item.RightFoot.trim() !== "" ?
                            <span title={`Right Foot: ${item.RightFoot}`}>
                                <Icon icon="mdi:foot-print" className={`w-5 h-5 inline-block ${getFootIconColor(item.RightFoot)}`} style={{ transform: 'scaleX(-1)' }} aria-label={`Right foot strength: ${item.RightFoot}`} />
                            </span>
                            : <span className="text-slate-500">—</span>
                        )}
                        {header.key === 'avgGoalkeepingScore' && renderAverageScoreCell(item.avgGoalkeepingScore)}
                        {header.key === 'avgTechnicalScore' && renderAverageScoreCell(item.avgTechnicalScore)}
                        {header.key === 'avgMentalScore' && renderAverageScoreCell(item.avgMentalScore)}
                        {header.key === 'avgPhysicalScore' && renderAverageScoreCell(item.avgPhysicalScore)}
                        {header.key === 'highScore' && <span className="text-cyan-300 font-semibold">{item.highScore.toFixed(2)}</span>}
                        {header.key === 'topRole' && renderRoleCell(item.topRole, 'text-pink-400')}
                        {header.key === 'secondRole' && renderRoleCell(item.secondRole, 'text-orange-400')}
                        {header.key === 'thirdRole' && renderRoleCell(item.thirdRole, 'text-indigo-400')}
                        {header.key === 'actionsColumn' && (
                            <button
                            onClick={() => onOpenPlayerModal(item)}
                            className="p-1.5 sm:p-2 bg-teal-500 hover:bg-teal-600 text-white rounded-full transition-colors duration-200
                                       focus:outline-none focus:ring-2 focus:ring-teal-300 focus:ring-opacity-50"
                            aria-label={`View attributes for ${item.playerName}`}
                            >
                            <EyeIcon className="w-4 h-4" />
                            </button>
                        )}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredAndSortedResults.length === 0 && (
           <div className="text-center text-slate-400 py-8">
             {(searchTerm || (appliedHighScoreRange.min && appliedHighScoreRange.min !== '') || (appliedHighScoreRange.max && appliedHighScoreRange.max !== ''))
               ? 'No players match your current filter criteria.'
               : 'No results to display. Process data to see scores.'
             }
           </div>
        )}

        {totalItems > 0 && (
          <div className="flex flex-col sm:flex-row items-center justify-between mt-4 md:mt-6 pt-4 border-t border-slate-700 gap-3">
            <div className="flex items-center gap-2">
              <label htmlFor="itemsPerPageSelect" className="text-sm text-slate-400">Items per page:</label>
              <select
                id="itemsPerPageSelect"
                value={itemsPerPage}
                onChange={handleItemsPerPageChange}
                className="bg-slate-700 text-slate-200 border border-slate-600 rounded-md p-1.5 text-sm focus:ring-1 focus:ring-purple-500 focus:border-purple-500"
                aria-label="Select number of items per page"
              >
                {itemsPerPageOptions.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm text-slate-400">
                Page {currentPage} of {totalPages} ({totalItems} items)
              </span>
              <button
                onClick={handlePreviousPage}
                disabled={currentPage === 1}
                className="p-1.5 sm:p-2 bg-slate-600 hover:bg-slate-500 text-slate-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                aria-label="Previous page"
                title="Previous page"
              >
                <Icon icon="mdi:chevron-left" className="w-5 h-5" />
              </button>
              <button
                onClick={handleNextPage}
                disabled={currentPage === totalPages || totalPages === 0}
                className="p-1.5 sm:p-2 bg-slate-600 hover:bg-slate-500 text-slate-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                aria-label="Next page"
                title="Next page"
              >
                <Icon icon="mdi:chevron-right" className="w-5 h-5" />
              </button>
            </div>
          </div>
        )}

      </section>
    </>
  );
};
