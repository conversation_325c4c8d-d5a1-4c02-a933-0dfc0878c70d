
import React, { useEffect } from 'react';
import type { AggregatedPlayerScore, Player } from '../types'; // AggregatedPlayerScore needed

interface PlayerAttributesModalProps {
  isOpen: boolean;
  onClose: () => void;
  playerData: AggregatedPlayerScore | null;
}

const getInitials = (name: string): string => {
  if (!name) return 'N/A';
  const parts = name.split(' ');
  if (parts.length > 1) {
    return `${parts[0][0]}${parts[parts.length - 1][0]}`.toUpperCase();
  }
  return name.substring(0, 2).toUpperCase();
};

const getAttributeColor = (value: number | string | undefined): string => {
  const numValue = Number(value);
  if (isNaN(numValue)) return 'text-slate-400'; // Default for non-numeric or N/A

  if (numValue >= 15) return 'text-green-400'; // Bright Green
  if (numValue >= 10) return 'text-sky-400';   // Light Blue/Teal
  if (numValue >= 1) return 'text-yellow-500'; // Dark Yellow/Orange
  return 'text-slate-400'; // For 0 or other cases
};

const formatCurrency = (value: string | number | undefined, perAnnum: boolean = false): string => {
    if (value === undefined || value === null) return "N/A";
    
    const originalStringValue = String(value);
    let num = parseFloat(originalStringValue.replace(/[^0-9.-]+/g,""));

    if (isNaN(num)) { // If original value was non-numeric string (e.g. "On Loan", "Free")
        const strVal = originalStringValue.trim();
        return strVal.length > 0 ? strVal : "N/A";
    }

    // Check for K/M multipliers from the original string value, if parser didn't handle it
    // This is a fallback, parser should ideally convert these.
    if (originalStringValue.toLowerCase().includes('k')) {
        num *= 1000;
    } else if (originalStringValue.toLowerCase().includes('m')) {
        num *= 1000000;
    }
    
    let formatted;
    if (num >= 1000000) {
        formatted = `$${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
        formatted = `$${(num / 1000).toFixed(0)}K`;
    } else {
      // For values less than 1000, display as is with $
      formatted = num.toLocaleString('en-US', { style: 'currency', currency: 'USD', minimumFractionDigits: 0, maximumFractionDigits: 0 });
    }

    return perAnnum ? `${formatted} p/a` : formatted;
};

const ValueCard: React.FC<{ title: string; value: string | React.ReactNode; score?: number | string; className?: string }> = ({ title, value, score, className = "" }) => (
  <div className={`bg-slate-800 p-3 md:p-4 rounded-lg shadow-md flex-1 ${className}`}>
    <h3 className="text-xs text-slate-400 uppercase tracking-wider mb-1 md:mb-1.5">{title}</h3>
    {typeof value === 'string' ? <p className="text-sm md:text-base font-semibold text-slate-100 truncate" title={value}>{value}</p> : value}
    {score !== undefined && <p className={`text-lg md:text-xl font-bold ${getAttributeColor(Number(score))}`}>{Number(score).toFixed(2)}</p>}
  </div>
);

const AttributeItem: React.FC<{ label: string; value: string | number | undefined }> = ({ label, value }) => (
  <>
    <span className="text-slate-300 text-xs md:text-sm">{label}</span>
    <span className={`font-semibold text-right text-xs md:text-sm ${getAttributeColor(value)}`}>
      {value !== undefined && !isNaN(Number(value)) ? Number(value) : (value !== undefined ? String(value) : 'N/A')}
    </span>
  </>
);

const AttributesCard: React.FC<{ title: string; attributes: Player; attributeKeys: string[] }> = ({ title, attributes, attributeKeys }) => {
  const midPoint = Math.ceil(attributeKeys.length / 2);
  const relevantAttributes = attributeKeys.filter(key => attributes[key] !== undefined && String(attributes[key]).trim() !== '');
  if (relevantAttributes.length === 0 && title !== "ROLE SUITABILITY") return null; 

  const displayKeys = relevantAttributes.length > 0 ? relevantAttributes : attributeKeys; 
  const displayMidPoint = Math.ceil(displayKeys.length / 2);


  return (
    <div className="bg-slate-800 p-3 md:p-4 rounded-lg shadow-md">
      <h4 className="text-sm md:text-base font-semibold text-slate-100 mb-2 md:mb-3">{title}</h4>
      <div className="grid grid-cols-[auto_minmax(20px,auto)_10px_auto_minmax(20px,auto)] gap-x-2 md:gap-x-3 gap-y-1 md:gap-y-1.5 items-baseline">
        {displayKeys.map((key, index) => (
          <React.Fragment key={key}>
            <AttributeItem label={key} value={attributes[key]} />
            {index + displayMidPoint < displayKeys.length && ( 
              <>
                <div /> 
                <AttributeItem label={displayKeys[index + displayMidPoint]} value={attributes[displayKeys[index + displayMidPoint]]} />
              </>
            )}
            {index < displayMidPoint && index + displayMidPoint >= displayKeys.length && (
                <>
                 <div /> <div/> <div />
                </>
            )}
          </React.Fragment>
        )).filter((_, index) => index < displayMidPoint) }
      </div>
    </div>
  );
};

// Define attribute keys for each category
const technicalAttributeKeys = ["Cor", "Cro", "Dri", "Fin", "Fir", "Fre", "Hea", "Lon", "Mar", "Pas", "Pen", "Tck", "Tec", "Thr"]; 
const mentalAttributeKeys = ["Agg", "Ant", "Bra", "Cmp", "Cnt", "Dec", "Det", "Fla", "Ldr", "OtB", "Pos", "Tea", "Vis", "Wor"];
const physicalAttributeKeys = ["Acc", "Agi", "Bal", "Jum", "Pac", "Sta", "Str"]; // "Nat" removed
const goalkeepingAttributeKeys = ["Aer", "Cmd", "Com", "Ecc", "Han", "Kic", "1v1", "Ref", "Rus", "TRO", "Pun"];


interface RoleSuitabilityDisplayItemProps {
  roleName: string;
  score: number;
}

const getScoreColorClasses = (score: number): { barClass: string; textClass: string } => {
  if (score >= 15) return { barClass: 'bg-teal-500', textClass: 'text-teal-400' }; // Excellent
  if (score >= 12) return { barClass: 'bg-sky-500', textClass: 'text-sky-400' };   // Good
  if (score >= 9) return { barClass: 'bg-yellow-500', textClass: 'text-yellow-400' }; // Average
  if (score >= 6) return { barClass: 'bg-orange-500', textClass: 'text-orange-400' }; // Below Average
  return { barClass: 'bg-red-500', textClass: 'text-red-400' }; // Poor
};

const RoleSuitabilityDisplayItem: React.FC<RoleSuitabilityDisplayItemProps> = ({
  roleName,
  score,
}) => {
  const { barClass, textClass } = getScoreColorClasses(score);
  const barPercentage = Math.min(Math.max((score / 20) * 100, 0), 100); // Assuming max score of 20

  return (
    <div className="bg-slate-700 p-3 rounded-lg flex-1 shadow min-w-[150px] sm:min-w-[180px]">
      <div className="flex justify-between items-baseline mb-1.5">
        <span className="text-slate-200 text-xs sm:text-sm font-medium truncate mr-2" title={roleName}>
          {roleName}
        </span>
        <span className={`font-bold text-xs sm:text-sm ${textClass}`}>
          {score.toFixed(2)}
        </span>
      </div>
      <div className="w-full bg-slate-600 rounded-full h-2">
        <div
          className={`h-full rounded-full ${barClass}`}
          style={{ width: `${barPercentage}%` }}
          role="progressbar"
          aria-valuenow={score}
          aria-valuemin={0}
          aria-valuemax={20}
          aria-label={`${roleName} suitability score ${score.toFixed(2)} out of 20`}
        ></div>
      </div>
    </div>
  );
};

export const PlayerAttributesModal: React.FC<PlayerAttributesModalProps> = ({
  isOpen,
  onClose,
  playerData,
}) => {
  useEffect(() => {
    const originalOverflow = document.body.style.overflow;
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = originalOverflow;
    }
    return () => {
      document.body.style.overflow = originalOverflow;
    };
  }, [isOpen]);

  if (!isOpen || !playerData) return null;

  const { playerName, allAttributes, Age, position } = playerData; // Removed allCalculatedRoles from destructuring here as it's not directly used after this change
  const initials = getInitials(playerName);
  
  const nationality = allAttributes && allAttributes.Nat ? String(allAttributes.Nat) : null;

  const playerSubInfo = [
    position,
    Age ? `${Age} years` : null,
    nationality ? nationality : null
  ].filter(Boolean).join(' • ');

  const salary = allAttributes?.Salary;
  const transferValue = allAttributes?.Value;

  return (
    <div
      className="fixed inset-0 bg-slate-950 bg-opacity-80 backdrop-blur-md flex items-center justify-center z-50 p-2 sm:p-4 selection:bg-purple-500 selection:text-white"
      onClick={onClose}
      role="dialog"
      aria-modal="true"
      aria-labelledby="playerAttributesModalTitle"
    >
      <div
        className="bg-slate-900 p-3 sm:p-4 md:p-5 rounded-xl shadow-2xl border border-slate-800 w-full max-w-2xl md:max-w-3xl max-h-[95vh] flex flex-col transform transition-all"
        onClick={e => e.stopPropagation()}
      >
        {/* Header Section */}
        <div className="flex items-center mb-3 md:mb-4 p-2">
          <div className="w-10 h-10 md:w-12 md:h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-base md:text-lg flex-shrink-0 mr-3 md:mr-4">
            {initials}
          </div>
          <div>
            <h2 id="playerAttributesModalTitle" className="text-lg md:text-xl font-bold text-slate-100">
              {playerName}
            </h2>
            {playerSubInfo && <p className="text-xs md:text-sm text-slate-400">{playerSubInfo}</p>}
          </div>
          <button
            onClick={onClose}
            className="ml-auto text-slate-400 hover:text-slate-200 transition-colors text-2xl leading-none"
            aria-label="Close modal"
          >&times;</button>
        </div>

        {/* Key Information Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 md:gap-3 mb-3 md:mb-4 px-1 sm:px-0">
          <ValueCard 
            title="TOP ROLE" 
            value={playerData.topRole?.name || "N/A"} 
            score={playerData.topRole?.score} 
          />
          <ValueCard 
            title="SALARY" 
            value={formatCurrency(salary, true)}
          />
          <ValueCard 
            title="TRANSFER VALUE" 
            value={formatCurrency(transferValue)} 
          />
        </div>

        {/* Main Attribute Display Area */}
        <div className="flex-grow overflow-y-auto space-y-2 md:space-y-3 custom-scrollbar pr-1 md:pr-2 -mr-1 md:-mr-2">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-2 md:gap-3">
            <AttributesCard title="Technical" attributes={allAttributes} attributeKeys={technicalAttributeKeys} />
            <AttributesCard title="Mental" attributes={allAttributes} attributeKeys={mentalAttributeKeys} />
            <AttributesCard title="Physical" attributes={allAttributes} attributeKeys={physicalAttributeKeys} />
            <AttributesCard title="Goalkeeping" attributes={allAttributes} attributeKeys={goalkeepingAttributeKeys} />
            
            <div className="bg-slate-800 p-3 md:p-4 rounded-lg shadow-md lg:col-span-2">
              <h4 className="text-sm md:text-base font-semibold text-slate-100 uppercase mb-3">Role Suitability</h4>
              
              {/* Top 3 Roles */}
              <div className="flex flex-col sm:flex-row gap-2 md:gap-3">
                {playerData.topRole && (
                  <RoleSuitabilityDisplayItem
                    roleName={playerData.topRole.name}
                    score={playerData.topRole.score}
                  />
                )}
                {playerData.secondRole && (
                  <RoleSuitabilityDisplayItem
                    roleName={playerData.secondRole.name}
                    score={playerData.secondRole.score}
                  />
                )}
                {playerData.thirdRole && (
                  <RoleSuitabilityDisplayItem
                    roleName={playerData.thirdRole.name}
                    score={playerData.thirdRole.score}
                  />
                )}
              </div>
              
              {/* Fallback if no roles at all */}
              {!playerData.topRole && (
                <p className="text-xs text-slate-400 mt-2">No role suitability data available for this player.</p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
