
import React, { useState, useEffect, useCallback } from 'react';
import type { AppManagedRole, AvailableAttributes, RoleDefinition } from '../types';
import { Spinner } from './Spinner'; 

interface RoleEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (role: AppManagedRole) => void;
  roleToEdit: AppManagedRole | null; 
  existingCategories: string[];
  availableAttributes: AvailableAttributes | null;
  attributesLoading: boolean;
  attributesError: string | null;
  disabled?: boolean;
  originalDefaultRolesMap: Map<string, RoleDefinition>; // Added to check for code conflicts
}

const initialRoleState = (role: AppManagedRole | null, isNew: boolean): Omit<AppManagedRole, 'isSelected'> => ({
  id: role?.id || crypto.randomUUID(), // Always generate new ID for a new role, or use existing for edit
  name: role?.name || '',
  code: role?.code || '',
  category: role?.category || '',
  key_attributes: role?.key_attributes || [],
  preferable_attributes: role?.preferable_attributes || [],
});


export const RoleEditModal: React.FC<RoleEditModalProps> = ({
  isOpen,
  onClose,
  onSave,
  roleToEdit,
  existingCategories,
  availableAttributes, 
  attributesLoading, 
  attributesError,   
  disabled,
  originalDefaultRolesMap
}) => {
  const [formData, setFormData] = useState<Omit<AppManagedRole, 'isSelected'>>(initialRoleState(roleToEdit, !roleToEdit));
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setFormData(initialRoleState(roleToEdit, !roleToEdit));
    setError(null); 
  }, [isOpen, roleToEdit]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleAttributeToggle = (attributeName: string, type: 'key' | 'preferable') => {
    if (disabled) return;
    setError(null); 

    setFormData(prev => {
      let currentKeyAttributes = [...prev.key_attributes];
      let currentPreferableAttributes = [...prev.preferable_attributes];

      if (type === 'key') {
        if (currentKeyAttributes.includes(attributeName)) {
          currentKeyAttributes = currentKeyAttributes.filter(attr => attr !== attributeName);
        } else {
          currentKeyAttributes.push(attributeName);
          currentPreferableAttributes = currentPreferableAttributes.filter(attr => attr !== attributeName);
        }
      } else { 
        if (currentPreferableAttributes.includes(attributeName)) {
          currentPreferableAttributes = currentPreferableAttributes.filter(attr => attr !== attributeName);
        } else {
          currentPreferableAttributes.push(attributeName);
          currentKeyAttributes = currentKeyAttributes.filter(attr => attr !== attributeName);
        }
      }
      return {
        ...prev,
        key_attributes: currentKeyAttributes,
        preferable_attributes: currentPreferableAttributes,
      };
    });
  };


  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (attributesLoading || (attributesError && !availableAttributes)) {
        setError("Attributes are still loading or failed to load. Please wait or resolve the issue before saving.");
        return;
    }
    if (!formData.name.trim() || !formData.code.trim()) {
      setError('Role Name and Code are required.');
      return;
    }
    if (!formData.category.trim()) {
      setError('Category is required.');
      return;
    }

    // Check for code conflict only if it's a new role OR if the code is being changed for an existing role
    // and the new code matches a default role's code (and it's not the same default role being edited)
    const isNewRole = !roleToEdit;
    const codeChanged = roleToEdit && roleToEdit.code !== formData.code.trim();

    if ((isNewRole || codeChanged) && originalDefaultRolesMap.has(formData.code.trim())) {
        // If it's a new role with a default code, or an existing role changing its code to a default code
        // (and it's not the original default role itself being edited with its original code).
        // This check prevents creating a "new" user role that masquerades as a default role via its code.
        // The ID will be different, but code collision with defaults for new/renamed roles is complex.
        // For simplicity, we allow editing a default role (identified by ID) even if its code remains the same.
        // We block a NEW role from taking a default role's code.
        // We block an EXISTING USER role from changing its code to a default role's code.
        // We block an EXISTING DEFAULT role from changing its code to ANOTHER default role's code.
        
        let conflictIsProblematic = false;
        if (isNewRole) { // A new role trying to use a default code
            conflictIsProblematic = true;
        } else if (codeChanged) { // An existing role changing its code
            // If roleToEdit was a user role, and it's changing its code to a default code
            if (!originalDefaultRolesMap.has(roleToEdit!.code) && originalDefaultRolesMap.has(formData.code.trim())) {
                 conflictIsProblematic = true;
            }
            // If roleToEdit was a default role, and it's changing its code to ANOTHER default role's code
            if (originalDefaultRolesMap.has(roleToEdit!.code) && originalDefaultRolesMap.has(formData.code.trim()) && roleToEdit!.code !== formData.code.trim()) {
                 conflictIsProblematic = true;
            }
        }

        if (conflictIsProblematic) {
             setError(`The code "${formData.code.trim()}" is reserved for a default role. Please use a different code.`);
             return;
        }
    }


    setError(null);
    onSave({ ...formData, isSelected: roleToEdit?.isSelected ?? true });
  };

  const renderAttributeGroup = (
    groupTitle: string,
    attributesInCategory: string[],
    selectedAttributes: string[],
    type: 'key' | 'preferable'
  ) => (
    <div key={`${type}-${groupTitle}`} className="mb-3">
      <h4 className="text-sm font-semibold text-cyan-300 mb-1.5 capitalize">{groupTitle}</h4>
      <div className="flex flex-wrap gap-1.5">
        {attributesInCategory.sort().map(attr => (
          <button
            key={attr}
            type="button"
            onClick={() => handleAttributeToggle(attr, type)}
            className={`px-2.5 py-1 rounded-md text-xs font-medium cursor-pointer transition-all duration-150 border
              ${selectedAttributes.includes(attr)
                ? (type === 'key' ? 'bg-blue-500 text-white hover:bg-blue-600 border-blue-400 shadow-md' : 'bg-purple-500 text-white hover:bg-purple-600 border-purple-400 shadow-md')
                : 'bg-slate-600 text-slate-300 hover:bg-slate-500 border-slate-500 hover:shadow-sm'
              }
              ${disabled ? 'opacity-70 cursor-not-allowed !shadow-none' : ''}
            `}
            disabled={disabled}
            aria-pressed={selectedAttributes.includes(attr)}
          >
            {attr}
          </button>
        ))}
      </div>
    </div>
  );

  if (!isOpen) return null;

  const attributeCategories = availableAttributes ? Object.keys(availableAttributes) : [];

  return (
    <div
      className="fixed inset-0 bg-slate-900 bg-opacity-80 backdrop-blur-sm flex items-center justify-center z-50 p-4"
      onClick={onClose}
      aria-modal="true"
      role="dialog"
    >
      <div
        className="bg-slate-800 p-6 md:p-8 rounded-xl shadow-2xl border border-slate-700 w-full max-w-2xl max-h-[90vh] flex flex-col transform transition-all"
        onClick={e => e.stopPropagation()}
      >
        <h2 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-purple-500 mb-6">
          {roleToEdit ? 'Edit Role' : 'Add New Role'}
        </h2>

        {error && (
          <div className="bg-red-700/30 border border-red-500 text-red-300 px-3 py-2 rounded-md mb-4 text-sm" role="alert">
            {error}
          </div>
        )}
        
        {attributesLoading && (
            <div className="flex items-center justify-center p-3 mb-3 bg-slate-700/50 rounded-md">
                <Spinner /><span className="ml-2 text-slate-300 text-sm">Loading attribute definitions...</span>
            </div>
        )}
        {attributesError && !availableAttributes && ( 
            <div className="bg-red-700/30 border border-red-500 text-red-300 px-3 py-2 rounded-md mb-4 text-sm" role="alert">
                <strong>Attribute Error:</strong> {attributesError} Cannot properly configure role attributes.
            </div>
        )}


        <form onSubmit={handleSubmit} className="space-y-4 flex-grow flex flex-col overflow-hidden">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-2">
            <div className="md:col-span-2">
              <label htmlFor="name" className="block text-sm font-medium text-purple-300 mb-1">Role Name</label>
              <input
                type="text"
                name="name"
                id="name"
                value={formData.name}
                onChange={handleChange}
                className="w-full p-2.5 bg-slate-700 text-slate-200 border-2 border-slate-600 rounded-md focus:border-purple-400 focus:ring-1 focus:ring-purple-400"
                required
                disabled={disabled}
              />
            </div>
            <div>
              <label htmlFor="code" className="block text-sm font-medium text-purple-300 mb-1">Role Code</label>
              <input
                type="text"
                name="code"
                id="code"
                value={formData.code}
                onChange={handleChange}
                className="w-full p-2.5 bg-slate-700 text-slate-200 border-2 border-slate-600 rounded-md focus:border-purple-400 focus:ring-1 focus:ring-purple-400"
                required
                disabled={disabled}
                maxLength={10} // Example max length
              />
            </div>
          </div>
           <div>
              <label htmlFor="category" className="block text-sm font-medium text-purple-300 mb-1">Category</label>
              <input
                type="text"
                name="category"
                id="category"
                value={formData.category}
                onChange={handleChange}
                list="category-suggestions"
                className="w-full p-2.5 bg-slate-700 text-slate-200 border-2 border-slate-600 rounded-md focus:border-purple-400 focus:ring-1 focus:ring-purple-400"
                required
                disabled={disabled}
              />
              <datalist id="category-suggestions">
                {existingCategories.map(cat => <option key={cat} value={cat} />)}
              </datalist>
            </div>

          <div className="flex-grow grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 overflow-y-auto custom-scrollbar pr-2 -mr-2 py-2">
            <section aria-labelledby="key-attributes-heading">
              <h3 id="key-attributes-heading" className="text-lg font-semibold text-blue-400 mb-2 sticky top-0 bg-slate-800 py-1 z-10">Key Attributes</h3>
              <div className="space-y-3">
                {(!availableAttributes && !attributesLoading && !attributesError) && <p className="text-slate-400 text-sm">Attribute list not available.</p> }
                {availableAttributes && attributeCategories.map(category =>
                  renderAttributeGroup(category, availableAttributes[category], formData.key_attributes, 'key')
                )}
              </div>
            </section>

            <section aria-labelledby="preferable-attributes-heading">
              <h3 id="preferable-attributes-heading" className="text-lg font-semibold text-purple-400 mb-2 sticky top-0 bg-slate-800 py-1 z-10">Preferable Attributes</h3>
              <div className="space-y-3">
                 {(!availableAttributes && !attributesLoading && !attributesError) && <p className="text-slate-400 text-sm">Attribute list not available.</p> }
                {availableAttributes && attributeCategories.map(category =>
                  renderAttributeGroup(category, availableAttributes[category], formData.preferable_attributes, 'preferable')
                )}
              </div>
            </section>
          </div>

          <div className="flex justify-end space-x-3 pt-4 mt-auto border-t border-slate-700">
            <button
              type="button"
              onClick={onClose}
              disabled={disabled}
              className="px-4 py-2 text-slate-300 bg-slate-600 hover:bg-slate-500 rounded-md transition-colors disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={disabled || attributesLoading || (attributesError && !availableAttributes) || !availableAttributes}
              className="px-4 py-2 text-white bg-gradient-to-r from-purple-600 to-cyan-500 hover:from-purple-700 hover:to-cyan-600 rounded-md transition-all shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:from-slate-600 disabled:to-slate-700"
              title={!availableAttributes || attributesError ? "Cannot save: Attributes data is missing or failed to load." : (attributesLoading ? "Waiting for attributes to load..." : "Save Role")}
            >
              Save Role
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
