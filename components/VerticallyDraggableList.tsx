import React, { useRef, useState, useCallback, useEffect } from 'react';

interface VerticallyDraggableListProps {
  children: React.ReactNode;
  maxHeight: string;
  className?: string;
  id?: string;
}

export const VerticallyDraggableList: React.FC<VerticallyDraggableListProps> = ({
  children,
  maxHeight,
  className,
  id
}) => {
  const scrollableRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startY, setStartY] = useState(0);
  const [scrollTopStart, setScrollTopStart] = useState(0);

  const handleMouseDown = useCallback((event: React.MouseEvent<HTMLDivElement>) => {
    if (!scrollableRef.current || event.button !== 0) return; // Only main button
    event.preventDefault(); // Prevent text selection/other default actions
    setIsDragging(true);
    setStartY(event.pageY);
    setScrollTopStart(scrollableRef.current.scrollTop);
    scrollableRef.current.style.cursor = 'grabbing';
    scrollableRef.current.style.userSelect = 'none';
  }, []);

  const handleTouchStart = useCallback((event: React.TouchEvent<HTMLDivElement>) => {
    if (!scrollableRef.current) return;
    // event.preventDefault(); // Allow native scroll to start if not dragging yet
    setIsDragging(true);
    setStartY(event.touches[0].pageY);
    setScrollTopStart(scrollableRef.current.scrollTop);
    scrollableRef.current.style.cursor = 'grabbing';
    scrollableRef.current.style.userSelect = 'none';
  }, []);

  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (!isDragging || !scrollableRef.current) return;
    event.preventDefault();
    const y = event.pageY;
    const walk = y - startY;
    scrollableRef.current.scrollTop = scrollTopStart - walk;
  }, [isDragging, startY, scrollTopStart]);

  const handleTouchMove = useCallback((event: TouchEvent) => {
    if (!isDragging || !scrollableRef.current) return;
    event.preventDefault(); // Prevent page scroll ONLY when actively dragging
    const y = event.touches[0].pageY;
    const walk = y - startY;
    scrollableRef.current.scrollTop = scrollTopStart - walk;
  }, [isDragging, startY, scrollTopStart]);

  const handleDragEnd = useCallback(() => {
    if (!isDragging) return;
    setIsDragging(false);
    if (scrollableRef.current) {
      scrollableRef.current.style.cursor = 'grab';
      scrollableRef.current.style.userSelect = '';
    }
  }, [isDragging]);

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleDragEnd);
      document.addEventListener('touchmove', handleTouchMove, { passive: false });
      document.addEventListener('touchend', handleDragEnd);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleDragEnd);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleDragEnd);
    };
  }, [isDragging, handleMouseMove, handleDragEnd, handleTouchMove]);

  return (
    <div
      id={id}
      ref={scrollableRef}
      className={`overflow-y-auto cursor-grab ${className || ''}`}
      style={{ maxHeight }}
      onMouseDown={handleMouseDown}
      onTouchStart={handleTouchStart}
      role="region" // Could be 'group' if it's a list of interactive items
      aria-label="Scrollable list of items" // More specific label might be needed depending on context
    >
      {children}
    </div>
  );
};
