
import React from 'react';
import { PlayerInput } from './PlayerInput';
import { RoleInput } from './RoleInput';

interface InputSectionProps {
  playerHtml: string; 
  setPlayerHtml: (html: string) => void;
  roleJson: string;
  setRoleJson: (json: string) => void;
  onProcess: () => void;
  isLoading: boolean;
  isProcessButtonDisabled: boolean;
}

export const InputSection: React.FC<InputSectionProps> = ({
  playerHtml,
  setPlayerHtml,
  roleJson,
  setRoleJson,
  onProcess,
  isLoading,
  isProcessButtonDisabled,
}) => {
  return (
    <section className="bg-slate-800 p-6 md:p-8 rounded-xl shadow-2xl border border-slate-700">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8 mb-8">
        <div className="h-full"> 
          <PlayerInput setPlayerHtml={setPlayerHtml} disabled={isLoading} />
        </div>
        {/* Ensure RoleInput can also expand if its content demands more height */}
        <div className="h-full">
          <RoleInput setRoleJson={setRoleJson} disabled={isLoading} />
        </div>
      </div>
      <button
        onClick={onProcess}
        disabled={isProcessButtonDisabled || isLoading} // Use the new prop here along with isLoading for the spinner state
        className={`w-full font-semibold py-4 px-6 rounded-2xl text-lg transition-all duration-300 ease-in-out
                    flex items-center justify-center
                    text-white border-2 border-white/80
                    bg-gradient-to-r from-fuchsia-500 via-purple-600 to-sky-400
                    
                    hover:from-fuchsia-600 hover:via-purple-700 hover:to-sky-500 hover:border-white hover:shadow-xl hover:shadow-purple-500/40
                    
                    shadow-lg shadow-purple-500/20 
                    
                    focus:outline-none focus:ring-4 focus:ring-purple-400/60
                    
                    disabled:opacity-70 disabled:cursor-not-allowed disabled:shadow-none
                    disabled:bg-gradient-to-r disabled:from-slate-600 disabled:via-slate-650 disabled:to-slate-700 
                    disabled:text-slate-400 disabled:border-slate-500`}
        aria-disabled={isProcessButtonDisabled || isLoading}
      >
        {isLoading ? (
          <>
            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Processing...
          </>
        ) : (
          'Process Data & Calculate Scores'
        )}
      </button>
    </section>
  );
};
