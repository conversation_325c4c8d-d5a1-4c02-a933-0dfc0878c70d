
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import type { AppManagedRole, RoleDefinition, RoleDefinitionsContainer, AvailableAttributes } from '../types';
import { RoleEditModal } from './RoleEditModal';
import { Spinner } from './Spinner';
import { VerticallyDraggableList } from './VerticallyDraggableList';
import { EditIcon } from './icons/EditIcon';
import { ChevronDownIcon } from './icons/ChevronDownIcon';
import { ChevronUpIcon } from './icons/ChevronUpIcon';
import { CheckboxCheckedIcon } from './icons/CheckboxCheckedIcon';
import { CheckboxUncheckedIcon } from './icons/CheckboxUncheckedIcon';
import { CheckCircleIcon } from './icons/CheckCircleIcon';
import { XCircleIcon } from './icons/XCircleIcon';
import { ExclamationTriangleIcon } from './icons/ExclamationTriangleIcon';
import { DeleteBinIcon } from './icons/DeleteBinIcon';
import { RestoreIcon } from './icons/RestoreIcon';

interface RoleInputProps {
  setRoleJson: (json: string) => void;
  disabled?: boolean;
}

const LOCAL_STORAGE_ROLES_KEY = 'userPlayerRoles';
const CATEGORY_ORDER = ["Goalkeeper", "Defenders", "Midfielders", "Attackers"];
const MAX_VISIBLE_ROLES_BEFORE_SCROLL = 4;
const ROLE_ITEM_ESTIMATED_HEIGHT_REM = 2.625; // Approx 2.2rem for item + 0.4rem for part of space-y. 2.625 * 4 = 10.5rem

const areAttributeArraysEqual = (arr1: string[], arr2: string[]): boolean => {
  if (arr1.length !== arr2.length) return false;
  const sortedArr1 = [...arr1].sort();
  const sortedArr2 = [...arr2].sort();
  return sortedArr1.every((val, index) => val === sortedArr2[index]);
};

export const RoleInput: React.FC<RoleInputProps> = ({ setRoleJson, disabled }) => {
  const [allRoles, setAllRoles] = useState<AppManagedRole[]>([]);
  const [isLoadingRoles, setIsLoadingRoles] = useState<boolean>(true);
  const [rolesError, setRolesError] = useState<string | null>(null);
  
  const [availableAttributes, setAvailableAttributes] = useState<AvailableAttributes | null>(null);
  const [isLoadingAttributes, setIsLoadingAttributes] = useState<boolean>(true);
  const [attributesError, setAttributesError] = useState<string | null>(null);

  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [roleToEdit, setRoleToEdit] = useState<AppManagedRole | null>(null);

  const [expandedCategory, setExpandedCategory] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const [originalDefaultRolesMap, setOriginalDefaultRolesMap] = useState<Map<string, RoleDefinition>>(new Map());

  const updateParentRoleJson = useCallback((currentRoles: AppManagedRole[]) => {
    const selectedRoles = currentRoles.filter(role => role.isSelected);
    const rolesForJson: RoleDefinition[] = selectedRoles.map(({ id, isSelected, ...rest }) => rest);
    const roleContainer: RoleDefinitionsContainer = { roles: rolesForJson };
    setRoleJson(JSON.stringify(roleContainer, null, 2));
  }, [setRoleJson]);

  const saveRolesToLocalStorage = useCallback((rolesToSave: AppManagedRole[]) => {
    try {
      localStorage.setItem(LOCAL_STORAGE_ROLES_KEY, JSON.stringify(rolesToSave));
    } catch (e: any) {
      console.error("Error saving roles to localStorage:", e);
      setRolesError(`Failed to save role changes to browser storage: ${e.message}. Some changes might not persist.`);
    }
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoadingRoles(true);
      setRolesError(null);
      setIsLoadingAttributes(true);
      setAttributesError(null);

      let rolesLoadedFromLocalStorage = false;
      let localRolesData: AppManagedRole[] = [];

      try {
        const rolesResponse = await fetch('data/roles_data.json');
        if (!rolesResponse.ok) throw new Error(`Failed to fetch roles_data.json: ${rolesResponse.status} ${rolesResponse.statusText}`);
        const defaultRolesContainer: RoleDefinitionsContainer = await rolesResponse.json();
        if (!defaultRolesContainer.roles || !Array.isArray(defaultRolesContainer.roles)) throw new Error("Invalid default roles data format.");
        
        const tempOriginalMap = new Map<string, RoleDefinition>();
        defaultRolesContainer.roles.forEach(role => {
          tempOriginalMap.set(role.code, { ...role, category: role.category || "Uncategorized" });
        });
        setOriginalDefaultRolesMap(tempOriginalMap);
        
        const defaultAppManagedRoles = defaultRolesContainer.roles.map(role => ({
          ...role,
          category: role.category || "Uncategorized",
          id: role.code, 
          isSelected: true,
        }));

        try {
          const storedRolesJson = localStorage.getItem(LOCAL_STORAGE_ROLES_KEY);
          if (storedRolesJson) {
            const storedRoles: AppManagedRole[] = JSON.parse(storedRolesJson);
            if (Array.isArray(storedRoles) && storedRoles.every(role => typeof role.name === 'string' && typeof role.id === 'string' && typeof role.category === 'string')) {
              localRolesData = storedRoles;
              rolesLoadedFromLocalStorage = true;
            } else {
              console.warn("Stored roles data is invalid. Falling back to default roles.");
              localStorage.removeItem(LOCAL_STORAGE_ROLES_KEY);
              localRolesData = defaultAppManagedRoles;
            }
          } else {
            localRolesData = defaultAppManagedRoles;
          }
        } catch (e: any) {
          console.error("Error loading roles from localStorage:", e);
          setRolesError(`Error loading saved roles: ${e.message}. Falling back to defaults.`);
          localStorage.removeItem(LOCAL_STORAGE_ROLES_KEY);
          localRolesData = defaultAppManagedRoles;
        }
        
        setAllRoles(localRolesData);
        updateParentRoleJson(localRolesData);
        setRolesError(null);

      } catch (e: any) {
        console.error("Error fetching or processing roles_data.json:", e);
        setRolesError(`Could not load default roles: ${e.message}. Any existing saved roles will be used, or an empty list if none.`);
        if (!rolesLoadedFromLocalStorage) {
             try {
                const storedRolesJson = localStorage.getItem(LOCAL_STORAGE_ROLES_KEY);
                if (storedRolesJson) {
                    localRolesData = JSON.parse(storedRolesJson);
                    if (!Array.isArray(localRolesData)) localRolesData = [];
                } else {
                    localRolesData = [];
                }
             } catch (lsError) {
                 localRolesData = [];
             }
            setAllRoles(localRolesData);
            updateParentRoleJson(localRolesData);
        }
      } finally {
        setIsLoadingRoles(false);
      }
      
      try {
        const attributesResponse = await fetch('data/attributes.json');
        if (!attributesResponse.ok) throw new Error(`Failed to fetch attributes: ${attributesResponse.status} ${attributesResponse.statusText}`);
        const attributesData: AvailableAttributes = await attributesResponse.json();
         if (!attributesData.physical || !attributesData.mental || !attributesData.technical || !attributesData.goalkeeping) {
            console.warn('Attributes JSON might be missing standard categories.');
        }
        setAvailableAttributes(attributesData);
        setAttributesError(null);
      } catch (e:any) {
        console.error("Error fetching attributes:", e);
        setAttributesError(`Could not load attributes: ${e.message}.`);
        setAvailableAttributes(null);
      } finally {
        setIsLoadingAttributes(false);
      }
    };
    fetchData();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setRoleJson, updateParentRoleJson]);


  const isUserAddedRole = useCallback((role: AppManagedRole): boolean => {
    return !originalDefaultRolesMap.has(role.code);
  }, [originalDefaultRolesMap]);

  const isDefaultRoleModified = useCallback((role: AppManagedRole): boolean => {
    if (isUserAddedRole(role)) return false;

    const originalRole = originalDefaultRolesMap.get(role.code);
    if (!originalRole) return false;

    const nameChanged = role.name !== originalRole.name;
    const categoryChanged = (role.category || "Uncategorized") !== (originalRole.category || "Uncategorized");
    const keyAttributesChanged = !areAttributeArraysEqual(role.key_attributes, originalRole.key_attributes);
    const preferableAttributesChanged = !areAttributeArraysEqual(role.preferable_attributes, originalRole.preferable_attributes);
    
    return nameChanged || categoryChanged || keyAttributesChanged || preferableAttributesChanged;
  }, [originalDefaultRolesMap, isUserAddedRole]);


  const handleToggleSelectRole = (roleId: string) => {
    if (disabled) return;
    const updatedRoles = allRoles.map(role =>
      role.id === roleId ? { ...role, isSelected: !role.isSelected } : role
    );
    setAllRoles(updatedRoles);
    updateParentRoleJson(updatedRoles);
    saveRolesToLocalStorage(updatedRoles);
  };

  const handleSelectAll = useCallback(() => {
    if (disabled) return;
    const updatedRoles = allRoles.map(role => ({ ...role, isSelected: true }));
    setAllRoles(updatedRoles);
    updateParentRoleJson(updatedRoles);
    saveRolesToLocalStorage(updatedRoles);
  }, [allRoles, disabled, updateParentRoleJson, saveRolesToLocalStorage]);

  const handleDeselectAll = useCallback(() => {
    if (disabled) return;
    const updatedRoles = allRoles.map(role => ({ ...role, isSelected: false }));
    setAllRoles(updatedRoles);
    updateParentRoleJson(updatedRoles);
    saveRolesToLocalStorage(updatedRoles);
  }, [allRoles, disabled, updateParentRoleJson, saveRolesToLocalStorage]);

  const areAllSelected = useMemo(() => allRoles.length > 0 && allRoles.every(role => role.isSelected), [allRoles]);
  const areNoneSelected = useMemo(() => allRoles.length > 0 && allRoles.every(role => !role.isSelected), [allRoles]);


  const handleOpenEditModal = (role: AppManagedRole) => {
    if (disabled) return;
    setRoleToEdit(role);
    setIsModalOpen(true);
  };

  const handleOpenAddModal = () => {
    if (disabled) return;
    setRoleToEdit(null); 
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setRoleToEdit(null);
  };

  const handleSaveRole = (savedRole: AppManagedRole) => {
    let updatedRoles;
    const existingRole = allRoles.find(r => r.id === savedRole.id);

    if (existingRole) { 
      updatedRoles = allRoles.map(r => r.id === savedRole.id ? savedRole : r);
    } else { 
      const newRoleWithId = { ...savedRole, id: savedRole.id || crypto.randomUUID() };
      updatedRoles = [...allRoles, newRoleWithId];
    }
    setAllRoles(updatedRoles);
    
    const newCategory = savedRole.category || 'Uncategorized';
    if (expandedCategory !== newCategory && !existingRole) {
        setExpandedCategory(newCategory); // Expand the new category if a new role was added to it
    }


    updateParentRoleJson(updatedRoles);
    saveRolesToLocalStorage(updatedRoles);
    handleCloseModal();

    const message = existingRole ? `Role "${savedRole.name}" updated successfully.` : `Role "${savedRole.name}" added successfully.`;
    setSuccessMessage(message);
    setTimeout(() => setSuccessMessage(null), 3000);
  };

  const handleDeleteRole = (roleIdToDelete: string) => {
    if (disabled) return;
    const roleToDelete = allRoles.find(r => r.id === roleIdToDelete);
    if (!roleToDelete || !isUserAddedRole(roleToDelete)) {
      setRolesError("Error: Cannot delete. This role is either a default role or does not exist.");
      return;
    }

    const updatedRoles = allRoles.filter(role => role.id !== roleIdToDelete);
    setAllRoles(updatedRoles);
    updateParentRoleJson(updatedRoles);
    saveRolesToLocalStorage(updatedRoles);
    setSuccessMessage(`Role "${roleToDelete.name}" deleted successfully.`);
    setTimeout(() => setSuccessMessage(null), 3000);
  };

  const handleRestoreRole = (roleIdToRestore: string) => {
    if (disabled) return;
    const currentRole = allRoles.find(r => r.id === roleIdToRestore);
    if (!currentRole) {
        setRolesError("Error: Role to restore not found.");
        return;
    }

    const originalDefaultRole = originalDefaultRolesMap.get(currentRole.code);
    if (!originalDefaultRole) {
        setRolesError(`Error: Original default definition for role code "${currentRole.code}" not found. Cannot restore.`);
        return;
    }

    const restoredRole: AppManagedRole = {
        ...originalDefaultRole,
        id: currentRole.id,
        isSelected: currentRole.isSelected,
        category: originalDefaultRole.category || "Uncategorized",
    };
    
    const updatedRoles = allRoles.map(role => role.id === roleIdToRestore ? restoredRole : role);
    setAllRoles(updatedRoles);
    updateParentRoleJson(updatedRoles);
    saveRolesToLocalStorage(updatedRoles);
    setSuccessMessage(`Role "${restoredRole.name}" restored to defaults.`);
    setTimeout(() => setSuccessMessage(null), 3000);
  };


  const toggleCategory = (categoryName: string) => {
    setExpandedCategory(prevExpanded => (prevExpanded === categoryName ? null : categoryName));
  };

  const rolesByCategory = useMemo(() => {
    return allRoles.reduce((acc, role) => {
      const category = role.category || 'Uncategorized';
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(role);
      return acc;
    }, {} as Record<string, AppManagedRole[]>);
  }, [allRoles]);
  
  const sortedCategories = useMemo(() => {
    const categoriesPresent = Object.keys(rolesByCategory);
    const orderedStandardCategories = CATEGORY_ORDER.filter(cat => categoriesPresent.includes(cat));
    const otherCategories = categoriesPresent
        .filter(cat => !CATEGORY_ORDER.includes(cat) && cat !== 'Uncategorized')
        .sort();
    const uncategorizedPresent = categoriesPresent.includes('Uncategorized') ? ['Uncategorized'] : [];
    
    return [...orderedStandardCategories, ...otherCategories, ...uncategorizedPresent];
  }, [rolesByCategory]);

  const renderStatusIndicator = () => {
    if (isLoadingRoles && !originalDefaultRolesMap.size) {
      return <><Spinner className="w-5 h-5 mr-2" /> <span className="text-sm text-slate-400">Loading roles...</span></>;
    }
    if (rolesError && allRoles.length === 0) { 
      return <><XCircleIcon className="w-5 h-5 mr-2 text-red-500" /> <span className="text-sm text-red-400">Roles: Critical Error</span></>;
    }
     if (rolesError) { 
      return <><ExclamationTriangleIcon className="w-5 h-5 mr-2 text-yellow-500" /> <span className="text-sm text-yellow-400">Roles: Notice</span></>;
    }
    if (isLoadingAttributes) {
      return <><Spinner className="w-5 h-5 mr-2" /> <span className="text-sm text-slate-400">Loading attributes...</span></>;
    }
    if (attributesError) {
      return <><ExclamationTriangleIcon className="w-5 h-5 mr-2 text-yellow-500" /> <span className="text-sm text-yellow-400">Attributes: Load Error</span></>;
    }
    if (availableAttributes && originalDefaultRolesMap.size > 0) { 
      return <><CheckCircleIcon className="w-5 h-5 mr-2 text-green-500" /> <span className="text-sm text-green-400">Ready</span></>;
    }
    return null; 
  };

  if (isLoadingRoles && !originalDefaultRolesMap.size && allRoles.length === 0 && !rolesError) { 
    return (
      <div className="h-full flex flex-col items-center justify-center bg-slate-800 p-6 md:p-8 rounded-xl shadow-xl border border-slate-700 min-h-[400px]">
        <Spinner />
        <p className="mt-3 text-purple-400">Loading Roles & Attributes...</p>
      </div>
    );
  }
  
  if (rolesError && allRoles.length === 0 && !isLoadingRoles) { 
    return (
      <div className="h-full flex flex-col items-center justify-center bg-slate-800 p-6 md:p-8 rounded-xl shadow-xl border border-slate-700 min-h-[400px]">
        <p className="text-red-400 text-center mb-4">{rolesError}</p>
         <button
          onClick={handleOpenAddModal}
          disabled={disabled || isLoadingAttributes || !availableAttributes} 
          className="py-2 px-4 bg-purple-600 hover:bg-purple-700 
                     text-white text-sm font-medium rounded-md transition-colors duration-200
                     focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-opacity-50
                     disabled:bg-slate-500 disabled:cursor-not-allowed"
        >
          + Add New Role Manually
        </button>
         {isLoadingAttributes && <p className="text-xs text-slate-400 mt-2">Waiting for attributes to load...</p>}
         {attributesError && !availableAttributes && <p className="text-xs text-red-400 mt-2">{attributesError} Cannot add role without attributes.</p>}
      </div>
    );
  }

  return (
    <>
      {successMessage && (
          <div className="fixed top-20 right-8 bg-green-600/95 border border-green-500 text-white px-6 py-3 rounded-lg shadow-xl text-sm z-[1000] animate-fadeInUp"
               role="status"
               aria-live="polite">
              {successMessage}
          </div>
      )}
      <div className={`bg-slate-800 p-4 md:p-6 rounded-xl shadow-2xl border border-slate-700 h-full flex flex-col ${disabled ? 'opacity-70' : ''}`}>
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-2xl md:text-3xl font-bold text-slate-100">Player Roles</h2>
          <div className="flex items-center">
            {renderStatusIndicator()}
          </div>
        </div>

        { (rolesError && allRoles.length > 0) && 
            <p className="text-yellow-400 text-xs text-center py-1 mb-2">(Note: {rolesError})</p>
        }

        {sortedCategories.length > 0 && !isLoadingRoles && (
          <p className="text-slate-400 text-xs md:text-sm mb-3 text-center md:text-left px-1">
            Click a category to expand. Only one category can be open. Drag vertically to scroll role lists.
          </p>
        )}

        <div className="flex-grow overflow-y-auto space-y-3 pr-2 custom-scrollbar min-h-[200px]">
          {sortedCategories.length === 0 && !isLoadingRoles && (
             <p className="text-slate-400 text-center py-6">No roles defined. Click "+ Add Role" to create one.</p>
          )}
          {sortedCategories.map(category => {
            const isExpanded = expandedCategory === category;
            const rolesInThisCategory = rolesByCategory[category] || [];
            const listMaxHeight = rolesInThisCategory.length > MAX_VISIBLE_ROLES_BEFORE_SCROLL 
              ? `${MAX_VISIBLE_ROLES_BEFORE_SCROLL * ROLE_ITEM_ESTIMATED_HEIGHT_REM}rem` 
              : 'auto';

            return (
            <div key={category} className="bg-slate-700/30 rounded-lg">
              <button 
                onClick={() => toggleCategory(category)}
                className="w-full flex justify-between items-center p-3 text-left text-purple-300 font-semibold hover:bg-slate-700/50 rounded-t-lg transition-colors"
                aria-expanded={isExpanded}
                aria-controls={`category-content-${category.replace(/\s+/g, '-').toLowerCase()}`}
              >
                <span>{category} ({rolesInThisCategory.length})</span>
                {isExpanded ? <ChevronUpIcon className="w-5 h-5" /> : <ChevronDownIcon className="w-5 h-5" />}
              </button>
              {isExpanded && (
                <VerticallyDraggableList
                  id={`category-content-${category.replace(/\s+/g, '-').toLowerCase()}`}
                  maxHeight={listMaxHeight}
                  className="border-t border-slate-600 custom-scrollbar"
                >
                  <ul className="p-3 space-y-2"> {/* Padding inside the scrollable area */}
                    {rolesInThisCategory.sort((a,b) => a.name.localeCompare(b.name)).map(role => {
                      const isDeletable = isUserAddedRole(role);
                      const isRestorable = !isDeletable && isDefaultRoleModified(role);
                      const commonIconDisabled = disabled || isLoadingAttributes || !availableAttributes;

                      return (
                      <li key={role.id} className="flex items-center justify-between p-2 rounded-md hover:bg-slate-600/50 transition-colors group">
                        <button
                          onClick={() => handleToggleSelectRole(role.id)}
                          className="flex items-center gap-2 text-slate-200 cursor-pointer focus:outline-none"
                          aria-pressed={role.isSelected}
                          disabled={disabled}
                        >
                          {role.isSelected ? 
                            <CheckboxCheckedIcon className="w-5 h-5 text-indigo-400 group-hover:text-indigo-300" />
                            : 
                            <CheckboxUncheckedIcon className="w-5 h-5 text-slate-500 group-hover:text-slate-400" />
                          }
                          <span className="truncate max-w-[150px] sm:max-w-[200px] md:max-w-xs" title={role.name}>{role.name} <span className="text-xs text-slate-400">({role.code})</span></span>
                        </button>
                        <div className="flex items-center gap-1.5 flex-shrink-0">
                          <button
                            onClick={() => handleOpenEditModal(role)}
                            disabled={commonIconDisabled}
                            className="p-1 text-slate-400 hover:text-cyan-400 rounded opacity-50 group-hover:opacity-100 transition-opacity focus:outline-none focus:ring-1 focus:ring-cyan-400 disabled:opacity-30 disabled:cursor-not-allowed"
                            aria-label={`Edit role ${role.name}`}
                            title={isLoadingAttributes ? "Waiting for attributes to load..." : (!availableAttributes ? "Attributes failed to load" : `Edit role ${role.name}`)}
                          >
                            <EditIcon className="w-4 h-4" />
                          </button>
                          {isDeletable && (
                            <button
                              onClick={() => handleDeleteRole(role.id)}
                              disabled={commonIconDisabled}
                              className="p-1 text-slate-400 hover:text-red-400 rounded opacity-50 group-hover:opacity-100 transition-opacity focus:outline-none focus:ring-1 focus:ring-red-400 disabled:opacity-30 disabled:cursor-not-allowed"
                              aria-label={`Delete role ${role.name}`}
                              title={`Delete role ${role.name}`}
                            >
                              <DeleteBinIcon className="w-4 h-4" />
                            </button>
                          )}
                          {isRestorable && (
                            <button
                              onClick={() => handleRestoreRole(role.id)}
                              disabled={commonIconDisabled}
                              className="p-1 text-slate-400 hover:text-yellow-400 rounded opacity-50 group-hover:opacity-100 transition-opacity focus:outline-none focus:ring-1 focus:ring-yellow-400 disabled:opacity-30 disabled:cursor-not-allowed"
                              aria-label={`Restore role ${role.name} to defaults`}
                              title={`Restore role ${role.name} to defaults`}
                            >
                              <RestoreIcon className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                      </li>
                    )})}
                  </ul>
                </VerticallyDraggableList>
              )}
            </div>
          )})}
        </div>
        
        <div className="flex justify-between items-center mt-auto pt-4 border-t border-slate-700">
          <div className="flex items-center gap-2">
            <button
              onClick={handleSelectAll}
              disabled={disabled || areAllSelected || allRoles.length === 0}
              className="py-2 px-4 bg-slate-700 hover:bg-slate-600 text-slate-100 border border-slate-600 text-sm font-medium rounded-md 
                         transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-opacity-50
                         disabled:opacity-50 disabled:cursor-not-allowed"
              aria-label="Select all roles"
            >
              Select All
            </button>
            <button
              onClick={handleDeselectAll}
              disabled={disabled || areNoneSelected || allRoles.length === 0}
              className="py-2 px-4 bg-slate-700 hover:bg-slate-600 text-slate-100 border border-slate-600 text-sm font-medium rounded-md 
                         transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-opacity-50
                         disabled:opacity-50 disabled:cursor-not-allowed"
              aria-label="Deselect all roles"
            >
              Deselect All
            </button>
          </div>
          <button
            onClick={handleOpenAddModal}
            disabled={disabled || isLoadingAttributes || !availableAttributes}
            className="py-2 px-4 bg-purple-600 hover:bg-purple-700 
                       text-white text-sm font-medium rounded-md transition-colors duration-200
                       focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-opacity-50
                       disabled:bg-slate-500 disabled:cursor-not-allowed"
            aria-label="Add new role"
            title={isLoadingAttributes ? "Waiting for attributes to load..." : (!availableAttributes ? "Attributes failed to load, cannot add role" : "Add new role")}
          >
            <span>+ Add Role</span>
          </button>
        </div>

        {isModalOpen && (
          <RoleEditModal
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            onSave={handleSaveRole}
            roleToEdit={roleToEdit}
            existingCategories={sortedCategories}
            availableAttributes={availableAttributes}
            attributesLoading={isLoadingAttributes}
            attributesError={attributesError}
            disabled={disabled && !!roleToEdit} 
            originalDefaultRolesMap={originalDefaultRolesMap}
          />
        )}
      </div>
    </>
  );
};
